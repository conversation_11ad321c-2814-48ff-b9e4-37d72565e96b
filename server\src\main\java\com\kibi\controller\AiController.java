package com.kibi.controller;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.gson.JsonObject;
import com.kibi.config.AiConfig;
import com.kibi.entity.*;
import com.kibi.service.*;
import com.kibi.utils.JWTUtils;
import com.kibi.utils.R;
import me.chanjar.weixin.mp.api.WxMpService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;

@RestController
@RequestMapping("/ai")
public class AiController {

    @Autowired
    private AiConfig.InitialMatchingAssistant initialMatchingAssistant;
    @Autowired
    private AiConfig.PartnerChatAssistant partnerChatAssistant;
    @Autowired
    private PartnerChatService partnerChatService;
    @Autowired
    private AiConfig.CreateAssistant createAssistant;
    @Autowired
    private JWTUtils jwtUtils;
    @Autowired
    private UserService userService;
    @Autowired
    private RoleService roleService;
    @Autowired
    private PartnerService partnerService;
    @Autowired
    private CreateChatService createChatService;

    //初始对话
    @PostMapping("/aiInitialMatching")
    public R<String> aiInitialMatching(String prompt, @RequestHeader("Authorization") String authHeader) {
        try {
            System.out.println("收到AI请求 - prompt: " + prompt);
            System.out.println("Authorization header: " + authHeader);
            
            // 验证token
            String token = jwtUtils.extractTokenFromHeader(authHeader);
            System.out.println("提取的token: " + token);
            
            if (token == null) {
                System.out.println("Token为空");
                return R.error("用户未登录或token无效");
            }
            
            if (!jwtUtils.validateToken(token)) {
                System.out.println("Token验证失败");
                return R.error("用户未登录或token无效");
            }

            // 获取用户ID
            Long userId = jwtUtils.getUserIdFromToken(token);
            if (userId == null) {
                return R.error("用户未登录");
            }

            User user = userService.getById(userId);
            if (user == null) return R.error("没有此用户");

            Role role = roleService.getById(userId);
            if (role != null) return R.error("服务器错误");

            Partner partner = partnerService.getById(userId);
            if (partner != null) return R.error("服务器错误");

            // 验证输入内容
            if (prompt == null || prompt.isEmpty()) {
                return R.error("请输入内容");
            }

            String res = initialMatchingAssistant.chat(userId, prompt);

            return R.success(res);

        } catch (Exception e) {
            System.err.println("AI异常: " + e.getMessage());
            return R.error("服务器错误");
        }
    }

    //星灵对话
    @PostMapping("/partnerChat")
    public R<String> partnerChat(String prompt, @RequestHeader("Authorization") String authHeader) {
        try {
            System.out.println("收到AI请求 - prompt: " + prompt);
            System.out.println("Authorization header: " + authHeader);

            // 验证token
            String token = jwtUtils.extractTokenFromHeader(authHeader);
            System.out.println("提取的token: " + token);

            if (token == null) {
                System.out.println("Token为空");
                return R.error("用户未登录或token无效");
            }

            if (!jwtUtils.validateToken(token)) {
                System.out.println("Token验证失败");
                return R.error("用户未登录或token无效");
            }

            // 获取用户ID
            Long userId = jwtUtils.getUserIdFromToken(token);
            if (userId == null) {
                return R.error("用户未登录");
            }

            User user = userService.getById(userId);
            if (user == null) return R.error("没有此用户");

            Role role = roleService.getById(userId);
            if (role == null) return R.error("暂无角色");

            Partner partner = partnerService.getById(userId);
            if (partner == null) return R.error("暂无星灵");

            // 验证输入内容
            if (prompt == null || prompt.isEmpty()) {
                return R.error("请输入内容");
            }

            LambdaQueryWrapper<PartnerChat> lqw = new LambdaQueryWrapper();
            lqw.eq(PartnerChat::getUserId, userId).eq(PartnerChat::getDiaryDate, LocalDate.now());
            PartnerChat one = partnerChatService.getOne(lqw);
            if (one == null) R.error("暂无日记");

            String res = partnerChatAssistant.chat(one.getId(), prompt);

            return R.success(res);

        } catch (Exception e) {
            System.err.println("AI异常: " + e.getMessage());
            return R.error("服务器错误");
        }
    }

    /**
     * 创建角色和AI伴侣
     */
    @PostMapping("/creator")
    public R creator(@RequestParam String nickname, @RequestHeader("Authorization") String authHeader) {
        try {
            // 验证token
            String token = jwtUtils.extractTokenFromHeader(authHeader);
            if (token == null || !jwtUtils.validateToken(token)) {
                return R.error("用户未登录或token无效");
            }

            // 获取当前用户ID
            Long currentUserId = jwtUtils.getUserIdFromToken(token);
            if (currentUserId == null) {
                return R.error("用户未登录");
            }

            // 检查用户是否存在
            User user = userService.getById(currentUserId);
            if (user == null) {
                return R.error("用户不存在");
            }

            // 检查是否已经创建过角色
            Role existingRole = roleService.getById(currentUserId);
            if (existingRole != null) {
                return R.error("您已经拥有角色");
            }

            // 检查是否已经创建过星灵
            Partner existingPartner = partnerService.getById(currentUserId);
            if (existingPartner != null) {
                return R.error("您已经拥有星灵");
            }

            // 获取用户的对话历史作为分析基础
            CreateChat createChat = createChatService.getById(currentUserId);
            
            // 构建对话历史字符串
            StringBuilder conversationHistory = new StringBuilder();
            conversationHistory.append("天空守护者: ").append("").append("\n");
            if (createChat != null && createChat.getMsg() != null) {
                JSONArray messages = JSONArray.parse(createChat.getMsg());
                for (int i = 0; i < messages.size(); i++) {
                    com.alibaba.fastjson2.JSONObject message = messages.getJSONObject(i);
                    String type = message.getString("type");
                    
                    if ("USER".equals(type)) {
                        JSONArray contents = message.getJSONArray("contents");
                        if (contents != null && contents.size() > 0) {
                            com.alibaba.fastjson2.JSONObject content = contents.getJSONObject(0);
                            String text = content.getString("text");
                            conversationHistory.append(user.getNickName() + ": ").append(text).append("\n");
                        }
                    } else if ("AI".equals(type)) {
                        String text = message.getString("text");
                        if (text != null && !text.isEmpty()) {
                            conversationHistory.append("天空守护者: ").append(text).append("\n");
                        }
                    }
                }
            }
            
            String prompt = "你是性格创造师，负责根据天空守护者与玩家的对话内容，为玩家和其AI伴侣星灵创建完整的角色档案。\n" +
                    "                \n" +
                    "                ## 任务要求：\n" +
                    "                基于对话内容分析玩家的性格特征、兴趣爱好、价值观念和情感需求，创建匹配的玩家档案和互补的星灵伴侣档案。\n" +
                    "                \n" +
                    "                玩家昵称：" + user.getNickName() + "\n" +
                    "                星灵昵称：" + nickname + "\n" +
                    "                \n" +
                    "                ## 角色属性规则：\n" +
                    "                1. **特质(traits)**：每个角色3个核心特质，用简洁的词汇描述，限制在2-3个字\n" +
                    "                2. **性格描述(description)**：\n" +
                    "                   - 玩家：一段完整的性格描述文字\n" +
                    "                   - 星灵：3句话的数组，描述其陪伴特点\n" +
                    "                3. **维度属性**：6个维度，每个维度最大值32，总和不超过96\n" +
                    "                   - spontaneous: 自发性/冲动性\n" +
                    "                   - collaborative: 合作性/社交性  \n" +
                    "                   - realist: 现实主义/务实性\n" +
                    "                   - logical: 逻辑性/理性\n" +
                    "                   - analytical: 分析性/思辨性\n" +
                    "                   - introvert: 内向性/独处倾向\n" +
                    "                \n" +
                    "                ## 匹配原则：\n" +
                    "                - 星灵应与玩家形成互补关系，而非完全相同\n" +
                    "                - 星灵的特质应能满足玩家的情感需求\n" +
                    "                - 维度属性要体现性格差异，创造有趣的互动动态\n" +
                    "                \n" +
                    "                ## 输出格式：\n" +
                    "                严格按照以下JSON格式输出，确保语法正确：\n" +
                    "                \n" +
                    "                ```json\n" +
                    "                {\n" +
                    "                  \"player\": {\n" +
                    "                    \"traits\": [\"特质1\", \"特质2\", \"特质3\"],\n" +
                    "                    \"description\": \"完整的性格描述段落\",\n" +
                    "                    \"spontaneous\": 数值,\n" +
                    "                    \"collaborative\": 数值,\n" +
                    "                    \"realist\": 数值,\n" +
                    "                    \"logical\": 数值,\n" +
                    "                    \"analytical\": 数值,\n" +
                    "                    \"introvert\": 数值\n" +
                    "                  },\n" +
                    "                  \"partner\": {\n" +
                    "                    \"traits\": [\"特质1\", \"特质2\", \"特质3\"],\n" +
                    "                    \"description\": [\n" +
                    "                      \"陪伴特点描述1\",\n" +
                    "                      \"陪伴特点描述2\", \n" +
                    "                      \"陪伴特点描述3\"\n" +
                    "                    ],\n" +
                    "                    \"spontaneous\": 数值,\n" +
                    "                    \"collaborative\": 数值,\n" +
                    "                    \"realist\": 数值,\n" +
                    "                    \"logical\": 数值,\n" +
                    "                    \"analytical\": 数值,\n" +
                    "                    \"introvert\": 数值\n" +
                    "                  }\n" +
                    "                }\n" +
                    "                ```\n" +
                    "                \n" +
                    "                请基于以下对话内容进行分析：" +
                    "                \n" + conversationHistory.toString();
            
            // 调用AI创建助手生成角色数据
            System.out.println(prompt);

            boolean isValidJSON = false;
            com.alibaba.fastjson2.JSONObject jsonResponse = null;
            int retryCount = 0;
            
            while (!isValidJSON) {
                try {
                    retryCount++;
                    if (retryCount == 10) return R.error("星灵创建失败");

                    String aiResponse = createAssistant.chat(prompt);
                    System.out.println("AI响应: " + aiResponse);

                    // 清理AI响应，移除markdown代码块标记
                    String cleanResponse = aiResponse.replaceAll("```json", "").replaceAll("```", "").trim();
                    
                    // 解析AI返回的JSON数据
                    jsonResponse = JSON.parseObject(cleanResponse);
                    
                    // 验证JSON结构是否完整
                    if (validateJSONStructure(jsonResponse)) {
                        isValidJSON = true;
                    }
                } catch (Exception e) {
                    System.out.println("JSON解析失败：" + e.getMessage());
                }
            }
            com.alibaba.fastjson2.JSONObject playerData = jsonResponse.getJSONObject("player");
            com.alibaba.fastjson2.JSONObject partnerData = jsonResponse.getJSONObject("partner");

            // 创建玩家角色
            Role role = new Role();
            role.setUserId(currentUserId.intValue());
            
            // 设置玩家特质
            JSONArray playerTraits = playerData.getJSONArray("traits");
            role.setTraits(playerTraits);
            
            // 设置玩家描述
            role.setDescription(playerData.getString("description"));
            
            // 设置玩家维度属性
            role.setSpontaneous(playerData.getInteger("spontaneous"));
            role.setCollaborative(playerData.getInteger("collaborative"));
            role.setRealist(playerData.getInteger("realist"));
            role.setLogical(playerData.getInteger("logical"));
            role.setAnalytical(playerData.getInteger("analytical"));
            role.setIntrovert(playerData.getInteger("introvert"));

            // 保存角色信息
            roleService.save(role);

            // 创建星灵伴侣
            Partner partner = new Partner();
            partner.setUserId(currentUserId.intValue());
            partner.setNickname(nickname.trim());
            
            // 设置星灵维度属性
            Integer spontaneous = partnerData.getInteger("spontaneous");
            Integer collaborative = partnerData.getInteger("collaborative");
            Integer realist = partnerData.getInteger("realist");
            Integer logical = partnerData.getInteger("logical");
            Integer analytical = partnerData.getInteger("analytical");
            Integer introvert = partnerData.getInteger("introvert");
            
            partner.setSpontaneous(spontaneous);
            partner.setCollaborative(collaborative);
            partner.setRealist(realist);
            partner.setLogical(logical);
            partner.setAnalytical(analytical);
            partner.setIntrovert(introvert);
            
            // 找出最大值对应的属性名
            String dominantTrait = "spontaneous";
            Integer maxValue = spontaneous;
            
            if (collaborative > maxValue) {
                maxValue = collaborative;
                dominantTrait = "collaborative";
            }
            if (realist > maxValue) {
                maxValue = realist;
                dominantTrait = "realist";
            }
            if (logical > maxValue) {
                maxValue = logical;
                dominantTrait = "logical";
            }
            if (analytical > maxValue) {
                maxValue = analytical;
                dominantTrait = "analytical";
            }
            if (introvert > maxValue) {
                maxValue = introvert;
                dominantTrait = "introvert";
            }
            
            partner.setStyle("/static/style/" + dominantTrait + ".png");
            
            // 设置星灵特质
            JSONArray partnerTraits = partnerData.getJSONArray("traits");
            partner.setTraits(partnerTraits);
            
            // 设置星灵描述（数组格式）
            JSONArray partnerDescription = partnerData.getJSONArray("description");
            partner.setDescription(partnerDescription);

            // 保存星灵信息
            partnerService.save(partner);

            return R.success("角色和星灵创建成功");

        } catch (Exception e) {
            e.printStackTrace();
            return R.error("系统异常：" + e.getMessage());
        }
    }

    /**
     * 验证JSON结构是否完整
     */
    private boolean validateJSONStructure(com.alibaba.fastjson2.JSONObject jsonResponse) {
        if (jsonResponse == null) {
            return false;
        }

        try {
            // 检查是否包含player和partner对象
            com.alibaba.fastjson2.JSONObject playerData = jsonResponse.getJSONObject("player");
            com.alibaba.fastjson2.JSONObject partnerData = jsonResponse.getJSONObject("partner");
            
            if (playerData == null || partnerData == null) {
                System.out.println("缺少player或partner对象");
                return false;
            }

            // 验证player对象必需字段
            if (!validatePlayerData(playerData)) {
                return false;
            }

            // 验证partner对象必需字段
            if (!validatePartnerData(partnerData)) {
                return false;
            }

            return true;
        } catch (Exception e) {
            System.out.println("JSON结构验证异常: " + e.getMessage());
            return false;
        }
    }

    /**
     * 验证player数据结构
     */
    private boolean validatePlayerData(com.alibaba.fastjson2.JSONObject playerData) {
        // 检查traits数组
        JSONArray traits = playerData.getJSONArray("traits");
        if (traits == null || traits.size() != 3) {
            System.out.println("player traits格式错误");
            return false;
        }

        // 检查description字符串
        String description = playerData.getString("description");
        if (description == null || description.trim().isEmpty()) {
            System.out.println("player description为空");
            return false;
        }

        // 检查维度属性
        String[] dimensions = {"spontaneous", "collaborative", "realist", "logical", "analytical", "introvert"};
        int totalValue = 0;
        
        for (String dimension : dimensions) {
            Integer value = playerData.getInteger(dimension);
            if (value == null || value < 0 || value > 32) {
                System.out.println("player " + dimension + " 值无效: " + value);
                return false;
            }
            totalValue += value;
        }

        if (totalValue > 96) {
            System.out.println("player 维度属性总和超过96: " + totalValue);
            return false;
        }

        return true;
    }

    /**
     * 验证partner数据结构
     */
    private boolean validatePartnerData(com.alibaba.fastjson2.JSONObject partnerData) {
        // 检查traits数组
        JSONArray traits = partnerData.getJSONArray("traits");
        if (traits == null || traits.size() != 3) {
            System.out.println("partner traits格式错误");
            return false;
        }

        // 检查description数组
        JSONArray description = partnerData.getJSONArray("description");
        if (description == null || description.size() != 3) {
            System.out.println("partner description格式错误");
            return false;
        }

        // 检查维度属性
        String[] dimensions = {"spontaneous", "collaborative", "realist", "logical", "analytical", "introvert"};
        int totalValue = 0;
        
        for (String dimension : dimensions) {
            Integer value = partnerData.getInteger(dimension);
            if (value == null || value < 0 || value > 32) {
                System.out.println("partner " + dimension + " 值无效: " + value);
                return false;
            }
            totalValue += value;
        }

        if (totalValue > 96) {
            System.out.println("partner 维度属性总和超过96: " + totalValue);
            return false;
        }

        return true;
    }
}
