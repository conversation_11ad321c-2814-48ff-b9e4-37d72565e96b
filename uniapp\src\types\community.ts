// 社区相关类型定义
export interface Post {
  id: number
  title: string
  content: string
  authorName: string
  authorAvatar: string
  categoryName: string
  createTime: string
  likeCount: number
  commentCount: number
  isLiked: boolean
  images: string[]
}

export interface Comment {
  id: number
  content: string
  authorName: string
  authorAvatar: string
  createTime: string
}

export interface Category {
  name: string
  key: string
}

export interface PublishForm {
  categoryIndex: number
  title: string
  content: string
  images: string[]
}