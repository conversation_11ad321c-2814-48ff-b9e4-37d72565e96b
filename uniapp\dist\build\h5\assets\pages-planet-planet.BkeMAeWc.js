import{d as e,af as a,I as t,y as l,A as s,ai as o,q as i,c as n,w as r,i as c,o as u,a as d,O as h,j as m,b as f,e as p,p as g,S as C,f as _,F as v,r as y,t as b,B as M,a4 as k,l as w,m as T,n as I,as as O,at as L,au as D}from"./index-C6kXbWaK.js";import{_ as x}from"./_plugin-vue_export-helper.BCo6x5W8.js";const A=x(e({__name:"planet",setup(e){var x;const A=null==(x=l())?void 0:x.appContext.config.globalProperties.$innerAudioContext,P=a();let S;t(()=>{A.loop=!0,A.autoplay=!0,A.src="/static/music/bg.mp3",A.onPlay(()=>{console.log("开始播放")}),<PERSON><PERSON>on<PERSON>r(e=>{console.log(e.errMsg),console.log(e.errCode)}),setTimeout(()=>{H()},100)});const E=e=>{const a={spontaneous:"随性",collaborative:"协作",realist:"务实",logical:"逻辑",analytical:"分析",introvert:"内向"};let t=0,l="随性";return Object.entries(e).forEach(([e,s])=>{s>t&&(t=s,l=a[e]||"随性")}),l},F=()=>{const e={spontaneous:0,collaborative:0,realist:0,logical:0,analytical:0,introvert:0},a=Object.keys(e);a.forEach(a=>{e[a]=Math.floor(8*Math.random())+1});let t=96-Object.values(e).reduce((e,a)=>e+a,0);for(;t>0;){const l=a[Math.floor(Math.random()*a.length)];if(e[l]<32){const a=Math.min(t,32-e[l],Math.floor(5*Math.random())+1);e[l]+=a,t-=a}}return e},R=s((()=>{const e=["小雪","阿尔法","小萌","墨轩","星辰","月影","晨曦","夜语","清风","雨露","紫霞","青云","白雪","红叶","金辉","银月","翠竹","梅花","兰草","菊香","桃花","柳絮","荷香","桂花","梧桐","松涛","竹韵","花语","鸟鸣","蝶舞","鱼跃","龙吟","凤鸣","虎啸","狼嚎","鹰翔","燕飞","雁归","鹤舞","孔雀","天使","精灵","仙子","女神","公主","王子","骑士","法师","战士","弓手","诗人","画家","音乐","舞者","歌手","作家","学者","智者","贤者","圣者","光明","黑暗","火焰","冰霜","雷电","大地","海洋","天空","星空","彩虹","梦境","幻想","现实","未来","过去","永恒","瞬间","无限","有限","完美","纯真","善良","勇敢","智慧","美丽","优雅","神秘","可爱","温柔","坚强","自由","和平","希望","信念","爱心","友谊","快乐","幸福","成功","胜利"],a=[["好奇","勇敢","创新"],["理性","睿智","逻辑"],["活泼","可爱","开朗"],["文艺","内敛","优雅"],["乐观","阳光","热情"],["沉稳","内敛","冷静"],["幽默","风趣","机智"],["神秘","优雅","高贵"],["勇敢","坚强","无畏"],["善良","纯真","温柔"],["聪明","机智","敏锐"],["浪漫","多情","感性"],["冷静","理智","客观"],["热情","奔放","活力"],["温文","尔雅","绅士"],["古灵","精怪","调皮"],["成熟","稳重","可靠"],["天真","烂漫","纯洁"],["独立","自主","坚定"],["细心","体贴","温暖"],["创意","无限","想象"],["逻辑","清晰","严谨"],["感性","丰富","细腻"],["理想","主义","追求"],["现实","主义","务实"],["完美","主义","精致"],["自由","奔放","洒脱"],["传统","保守","稳健"],["前卫","时尚","潮流"],["朴实","无华","真诚"]],t=["小雪","阿尔法","小萌","墨轩","星辰","月影","晨曦","夜语","清风","雨露","紫霞","青云","白雪","红叶","金辉","银月","翠竹","梅花","兰草","菊香","桃花","柳絮","荷香","桂花","梧桐","松涛","竹韵","花语","鸟鸣","蝶舞"],l=[["好奇","勇敢","创新"],["理性","睿智","逻辑"],["活泼","可爱","开朗"],["文艺","内敛","优雅"],["乐观","阳光","热情"],["沉稳","内敛","冷静"],["幽默","风趣","机智"],["神秘","优雅","高贵"],["勇敢","坚强","无畏"],["善良","纯真","温柔"],["聪明","机智","敏锐"],["浪漫","多情","感性"],["冷静","理智","客观"],["热情","奔放","活力"],["温文","尔雅","绅士"],["古灵","精怪","调皮"],["成熟","稳重","可靠"],["天真","烂漫","纯洁"],["独立","自主","坚定"],["细心","体贴","温暖"],["创意","无限","想象"],["逻辑","清晰","严谨"],["感性","丰富","细腻"],["理想","主义","追求"],["现实","主义","务实"],["完美","主义","精致"],["自由","奔放","洒脱"],["传统","保守","稳健"],["前卫","时尚","潮流"],["朴实","无华","真诚"]],s=["/static/style/introvert.png","/static/test1.png","/static/style/realist.png","/static/style/logical.png","/static/style/analytical.png","/static/style/collaborative.png","/static/style/spontaneous.png"],o=["你总是充满热情，善于与人交流，总是能理解他人的感受。","拥有超强逻辑思维，擅长解决复杂问题。","充满活力，总能带来欢声笑语。","富有艺术气息，喜欢诗词歌赋。","阳光开朗，积极向上。","沉稳内敛，深思熟虑。","幽默风趣，机智过人。","神秘优雅，气质非凡。","勇敢坚强，面对困难从不退缩。","善良纯真，心地善良。","聪明机智，思维敏捷。","浪漫多情，感情丰富。","冷静理智，客观公正。","热情奔放，充满活力。","温文尔雅，举止得体。","古灵精怪，充满创意。","成熟稳重，值得信赖。","天真烂漫，纯洁无瑕。","独立自主，坚定不移。","细心体贴，温暖如春。"],i=[["你总是充满热情，善于与人交流，总是能理解他人的感受。","你富有想象力，喜欢为生活增添色彩。","你的存在就像阳光一样，总能给周围的人带来温暖和快乐。"],["拥有超强逻辑思维，擅长解决复杂问题。","善于理性分析，提供客观建议。","是你最可靠的智慧伙伴。"],["充满活力，总能带来欢声笑语。","让生活充满乐趣和惊喜。","用快乐感染身边的每一个人。"],["富有艺术气息，喜欢诗词歌赋。","能够提供深度的精神交流。","用美好的文字温暖心灵。"],["阳光开朗，积极向上。","总是能看到事物美好的一面。","用乐观的态度面对一切挑战。"],["沉稳内敛，深思熟虑。","在喧嚣中保持内心的宁静。","用智慧和耐心解决问题。"],["幽默风趣，机智过人。","总能在适当的时候调节气氛。","用笑声化解生活中的烦恼。"],["神秘优雅，气质非凡。","总是能在关键时刻给出最合适的建议。","用独特的魅力吸引着他人。"],["勇敢坚强，面对困难从不退缩。","是你最可靠的伙伴。","用坚定的意志克服一切障碍。"],["善良纯真，心地善良。","用真诚和善意对待每一个人。","是这个世界上最美好的存在。"],["聪明机智，思维敏捷。","善于思考和分析，是你最好的智囊团。","用智慧照亮前进的道路。"],["浪漫多情，感情丰富。","用细腻的情感体验生活的美好。","是最懂得爱与被爱的存在。"],["冷静理智，客观公正。","在复杂的情况下保持清醒的头脑。","用理性的思维做出最佳决策。"],["热情奔放，充满活力。","用激情点燃生活的每一个瞬间。","是最有感染力的存在。"],["温文尔雅，举止得体。","用优雅的姿态面对生活。","是最有修养的绅士淑女。"],["古灵精怪，充满创意。","总能想出意想不到的点子。","用独特的思维方式看待世界。"],["成熟稳重，值得信赖。","在关键时刻总能挺身而出。","是最可靠的人生导师。"],["天真烂漫，纯洁无瑕。","用最纯真的心灵感受世界。","是这个世界上最珍贵的存在。"],["独立自主，坚定不移。","有着明确的目标和方向。","用自己的力量创造美好的未来。"],["细心体贴，温暖如春。","总是能察觉到他人的需要。","用关怀和爱心温暖着每一个人。"]],n=[];for(let r=5;r<=104;r++){const c=23.73+.09*(Math.random()-.5),u=113.09+.11*(Math.random()-.5),d=F(),h=F(),m=Math.floor(Math.random()*a.length),f=Math.floor(Math.random()*o.length),p=Math.floor(Math.random()*l.length),g=Math.floor(Math.random()*i.length),C=(new Date).toISOString();n.push({id:r.toString(),avatar:s[Math.floor(Math.random()*s.length)],nickname:e[Math.floor(Math.random()*e.length)],style:s[Math.floor(Math.random()*s.length)],createTime:C,role:{traits:a[m],description:o[f],attribute:d,partner:{nickName:t[Math.floor(Math.random()*t.length)],style:s[Math.floor(Math.random()*s.length)],createTime:C,traits:l[p],description:i[g],attribute:h}},position:{longitude:u,latitude:c,height:100},coreDimension:E(d)})}return n})()),z=s(null),B=s(!1),N=s(!1),$=113.09,U=23.73,j=1e4,G=()=>{S&&S.camera.flyTo({destination:Cesium.Cartesian3.fromDegrees($,U,j),duration:3,complete:()=>{o({title:"欢迎来到清远大学城",icon:"none"})}})},H=()=>{try{if("undefined"==typeof Cesium)return console.error("Cesium未正确加载"),void o({title:"Cesium未正确加载",icon:"none"});Cesium.Ion.defaultAccessToken="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiI2ZDY5NGRkMC04MThiLTRjYjYtYTkxOC1kYmU5ZGY5OGZmODAiLCJpZCI6MzIyMjQ3LCJpYXQiOjE3NTI3MjM3OTF9.IoUXBk4QpY_Mb--OsZIKzRXGFabLlugCOmCKJuNnwKA";const e={homeButton:!1,sceneModePicker:!1,fullscreenButton:!1,infoBox:!1,selectionIndicator:!1,baseLayerPicker:!1,shadows:!0,shouldAnimate:!0,animation:!1,timeline:!1,geocoder:!1,navigationHelpButton:!1,creditContainer:document.createElement("div")};S=new Cesium.Viewer("cesiumContainer",e),S&&S.cesiumWidget.screenSpaceEventHandler.setInputAction(e=>{const a=S.scene.pick(e.position);if(Cesium.defined(a)&&a.id&&a.id.properties){const e=a.id.properties.companionData;e&&(z.value=e._value||e,B.value=!0)}},Cesium.ScreenSpaceEventType.LEFT_CLICK),setTimeout(()=>{G(),J(),Y()},1e3)}catch(e){console.error("Cesium初始化失败:",e),o({title:"Cesium初始化失败",icon:"none"})}},W=()=>{B.value=!1,z.value=null},Z=e=>{S&&S.camera.flyTo({destination:Cesium.Cartesian3.fromDegrees(e.position.longitude,e.position.latitude,1e3),duration:2})},Y=()=>{if(S)try{const e=(()=>{const e=document.createElement("canvas");e.width=32,e.height=32;const a=e.getContext("2d");a.fillStyle="white",a.shadowColor="rgba(255, 255, 255, 0.8)",a.shadowBlur=4;const t=16,l=16,s=12;a.beginPath();for(let o=0;o<6;o++){const e=o*Math.PI/3,i=t+Math.cos(e)*s,n=l+Math.sin(e)*s,r=t+Math.cos(e)*(.3*s),c=l+Math.sin(e)*(.3*s);a.moveTo(t,l),a.lineTo(i,n),a.moveTo(r,c),a.lineTo(t+Math.cos(e+Math.PI/6)*(.6*s),l+Math.sin(e+Math.PI/6)*(.6*s)),a.moveTo(r,c),a.lineTo(t+Math.cos(e-Math.PI/6)*(.6*s),l+Math.sin(e-Math.PI/6)*(.6*s))}return a.strokeStyle="white",a.lineWidth=2,a.stroke(),a.beginPath(),a.arc(t,l,3,0,2*Math.PI),a.fill(),e.toDataURL()})();for(let a=0;a<3;a++){S.scene.primitives.add(new Cesium.ParticleSystem({image:e,startColor:Cesium.Color.WHITE.withAlpha(.9-.2*a),endColor:Cesium.Color.WHITE.withAlpha(.1),startScale:.8+.3*a,endScale:.2,particleLife:12+3*a,speed:3+2*a,imageSize:new Cesium.Cartesian2(8+4*a,8+4*a),emissionRate:800-200*a,lifetime:20,emitter:new Cesium.BoxEmitter(new Cesium.Cartesian3(3e4+1e4*a,3e4+1e4*a,2e3+1e3*a)),modelMatrix:Cesium.Transforms.eastNorthUpToFixedFrame(Cesium.Cartesian3.fromDegrees($,U,3e3+1e3*a)),gravity:-2.8-1*a,forces:[function(e){return new Cesium.Cartesian3(.5*Math.sin(.001*Date.now()+a),.3*Math.cos(.001*Date.now()+a),0)}]}))}S.scene.globe.enableLighting=!0,S.scene.globe.dynamicAtmosphereLighting=!0,S.scene.globe.dynamicAtmosphereLightingFromSun=!1,S.scene.skyAtmosphere.hueShift=-.3,S.scene.skyAtmosphere.saturationShift=-.6,S.scene.skyAtmosphere.brightnessShift=.2,S.scene.fog.enabled=!0,S.scene.fog.density=2e-4,S.scene.fog.screenSpaceErrorFactor=2,S.scene.light=new Cesium.DirectionalLight({direction:new Cesium.Cartesian3(.2,.5,-.8),color:new Cesium.Color(.9,.95,1,1),intensity:2}),console.log("雪地场景添加成功")}catch(e){console.error("添加雪地场景失败:",e)}},J=()=>{S&&R.value.forEach(e=>{S.entities.add({position:Cesium.Cartesian3.fromDegrees(e.position.longitude,e.position.latitude,e.position.height),billboard:{image:e.avatar,width:63,height:100,scale:1,verticalOrigin:Cesium.VerticalOrigin.BOTTOM,heightReference:Cesium.HeightReference.CLAMP_TO_GROUND,color:Cesium.Color.WHITE,outlineColor:Cesium.Color.fromCssColorString("#667eea"),outlineWidth:3,alignedAxis:Cesium.Cartesian3.UNIT_Z,rotation:0},label:{text:e.role.partner.nickName,font:"16pt sans-serif",fillColor:Cesium.Color.WHITE,outlineColor:Cesium.Color.BLACK,outlineWidth:2,style:Cesium.LabelStyle.FILL_AND_OUTLINE,verticalOrigin:Cesium.VerticalOrigin.BOTTOM,pixelOffset:new Cesium.Cartesian2(0,-100),heightReference:Cesium.HeightReference.CLAMP_TO_GROUND,backgroundColor:Cesium.Color.fromAlpha(Cesium.Color.BLACK,.7),backgroundPadding:new Cesium.Cartesian2(8,4),showBackground:!0},properties:{companionData:e}}),S.entities.add({position:Cesium.Cartesian3.fromDegrees(e.position.longitude,e.position.latitude,e.position.height),label:{text:e.coreDimension,font:"12pt sans-serif",fillColor:Cesium.Color.GOLD,outlineColor:Cesium.Color.BLACK,outlineWidth:2,style:Cesium.LabelStyle.FILL_AND_OUTLINE,verticalOrigin:Cesium.VerticalOrigin.BOTTOM,pixelOffset:new Cesium.Cartesian2(35,-60),heightReference:Cesium.HeightReference.CLAMP_TO_GROUND,backgroundColor:Cesium.Color.fromAlpha(Cesium.Color.PURPLE,.8),backgroundPadding:new Cesium.Cartesian2(6,3),showBackground:!0,scale:.8}})})},K=()=>{N.value&&(N.value=!1)},V=()=>{1==O().length?L({url:"/pages/ai/ai"}):D({delta:1})},X=()=>{o({title:"查看好友功能开发中",icon:"none"})},q=i(()=>{var e,a,t;if(!(null==(e=P.profile)?void 0:e.attribute))return[{label:"spontaneous",label_zh:"随性",skychart:0,you:0},{label:"collaborative",label_zh:"协作",skychart:0,you:0},{label:"realist",label_zh:"务实",skychart:0,you:0},{label:"logical",label_zh:"逻辑",skychart:0,you:0},{label:"analytical",label_zh:"分析",skychart:0,you:0},{label:"introvert",label_zh:"内向",skychart:0,you:0}];const l=(null==(a=P.profile)?void 0:a.attribute)||{},s=(null==(t=z.value)?void 0:t.role.attribute)||{};return[{label:"spontaneous",label_zh:"随性",skychart:s.spontaneous||0,you:l.spontaneous||0},{label:"collaborative",label_zh:"协作",skychart:s.collaborative||0,you:l.collaborative||0},{label:"realist",label_zh:"务实",skychart:s.realist||0,you:l.realist||0},{label:"logical",label_zh:"逻辑",skychart:s.logical||0,you:l.logical||0},{label:"analytical",label_zh:"分析",skychart:s.analytical||0,you:l.analytical||0},{label:"introvert",label_zh:"内向",skychart:s.introvert||0,you:l.introvert||0}]}),Q=()=>{const e=[];for(let a=1;a<=3;a++){const t=100*a/3;let l="";for(let e=0;e<6;e++){const a=(60*e-90)*Math.PI/180,s=150+t*Math.cos(a),o=150+t*Math.sin(a);0===e?l=`M ${s} ${o}`:l+=` L ${s} ${o}`}l+=" Z",e.push(l)}return e},ee=()=>{let e="";return q.value.forEach((a,t)=>{const l=(60*t-90)*Math.PI/180,s=a.skychart/32*100,o=150+s*Math.cos(l),i=150+s*Math.sin(l);0===t?e=`M ${o} ${i}`:e+=` L ${o} ${i}`}),e+=" Z",e},ae=()=>{let e="";return q.value.forEach((a,t)=>{const l=(60*t-90)*Math.PI/180,s=a.you/32*100,o=150+s*Math.cos(l),i=150+s*Math.sin(l);0===t?e=`M ${o} ${i}`:e+=` L ${o} ${i}`}),e+=" Z",e};return(e,a)=>{const t=m,l=c,s=f,o=C,i=T,O=w;return u(),n(l,{class:"planet-container"},{default:r(()=>[d(l,{class:"top-nav"},{default:r(()=>[d(l,{class:"nav-left"},{default:r(()=>[d(l,{class:"home-btn",onClick:V},{default:r(()=>[d(t,{src:"/static/ai/home.png",mode:"aspectFit"})]),_:1})]),_:1}),d(l,{class:"nav-center"},{default:r(()=>[d(l,{class:"qingyuan-btn",onClick:G},{default:r(()=>[d(s,null,{default:r(()=>[p("清远大学城")]),_:1})]),_:1})]),_:1}),d(l,{class:"nav-right"},{default:r(()=>[d(l,{class:"profile-btn",onClick:X},{default:r(()=>[d(t,{style:{width:"60rpx",height:"60rpx"},src:"/static/ai/friends.png",mode:"aspectFit"})]),_:1})]),_:1})]),_:1}),d(l,{class:g(["companion-list",{show:N.value}])},{default:r(()=>[d(l,{class:"list-header"},{default:r(()=>[d(s,{class:"title"},{default:r(()=>[p("附近的星灵")]),_:1}),d(l,{class:"close-btn",onClick:a[0]||(a[0]=e=>N.value=!1)},{default:r(()=>[p("×")]),_:1})]),_:1}),d(o,{class:"list-content","scroll-y":""},{default:r(()=>[(u(!0),_(v,null,y(R.value,e=>(u(),n(l,{class:"companion-item",key:e.id,onClick:a=>Z(e)},{default:r(()=>[d(l,{class:"character"},{default:r(()=>[d(t,{src:e.avatar,mode:"aspectFill"},null,8,["src"]),d(l,{class:"core-dimension"},{default:r(()=>[p(b(e.coreDimension),1)]),_:2},1024)]),_:2},1024),d(l,{class:"info"},{default:r(()=>[d(s,{class:"name"},{default:r(()=>[p(b(e.nickname),1)]),_:2},1024),d(l,{class:"traits-display"},{default:r(()=>[(u(!0),_(v,null,y(e.role.traits,e=>(u(),n(l,{key:e,class:"trait-tag"},{default:r(()=>[d(s,{class:"trait-text"},{default:r(()=>[p(b(e),1)]),_:2},1024)]),_:2},1024))),128))]),_:2},1024),d(s,{class:"owner"},{default:r(()=>[p("降临: "+b(new Date(e.createTime).toLocaleDateString()),1)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onClick"]))),128))]),_:1})]),_:1},8,["class"]),d(l,{class:"companion-toggle",onClick:a[1]||(a[1]=e=>N.value=!N.value)},{default:r(()=>[d(t,{src:"/static/ai/list.png",mode:"aspectFit"})]),_:1}),d(l,{class:"main-content",onClick:K},{default:r(()=>[M("div",{class:"cesium",id:"cesiumContainer",style:{width:"100%",height:"100vh"}})]),_:1}),B.value?(u(),n(l,{key:0,class:"companion-detail-modal",onClick:W},{default:r(()=>[d(l,{class:"detail-content",onClick:a[3]||(a[3]=k(()=>{},["stop"]))},{default:r(()=>[d(l,{class:"detail-header"},{default:r(()=>[d(l,{class:"character-large floating-avatar"},{default:r(()=>{var e;return[d(t,{src:null==(e=z.value)?void 0:e.avatar,mode:"aspectFill"},null,8,["src"]),d(l,{class:"core-dimension-badge"},{default:r(()=>{var e;return[p(b(null==(e=z.value)?void 0:e.coreDimension),1)]}),_:1})]}),_:1}),d(l,{class:"basic-info"},{default:r(()=>[d(s,{class:"name"},{default:r(()=>{var e;return[p(b(null==(e=z.value)?void 0:e.nickname),1)]}),_:1}),d(l,{class:"traits-display"},{default:r(()=>{var e;return[(u(!0),_(v,null,y(null==(e=z.value)?void 0:e.role.traits,e=>(u(),n(l,{key:e,class:"trait-tag"},{default:r(()=>[d(s,{class:"trait-text"},{default:r(()=>[p(b(e),1)]),_:2},1024)]),_:2},1024))),128))]}),_:1}),d(s,{class:"owner"},{default:r(()=>{var e;return[p("降临: "+b((null==(e=z.value)?void 0:e.createTime)?new Date(z.value.createTime).toLocaleDateString():""),1)]}),_:1})]),_:1}),d(l,{class:"close-btn",onClick:W},{default:r(()=>[p("×")]),_:1})]),_:1}),d(l,{class:"detail-body"},{default:r(()=>[d(l,{class:"partner-complete-section"},{default:r(()=>[d(l,{class:"partner-showcase-container"},{default:r(()=>[d(s,{class:"partner-name"},{default:r(()=>{var e;return[p(b(null==(e=z.value)?void 0:e.role.partner.nickName),1)]}),_:1}),d(l,{class:"traits-display-enhanced"},{default:r(()=>{var e;return[(u(!0),_(v,null,y(null==(e=z.value)?void 0:e.role.partner.traits,e=>(u(),n(l,{key:e,class:"trait-tag-highlight partner-trait"},{default:r(()=>[d(s,{class:"trait-text"},{default:r(()=>[p(b(e),1)]),_:2},1024)]),_:2},1024))),128))]}),_:1}),d(l,{class:"partner-avatar-container"},{default:r(()=>[d(l,{class:"character-avatar floating-avatar-delayed"},{default:r(()=>{var e;return[d(t,{src:null==(e=z.value)?void 0:e.role.partner.style,mode:"widthFix"},null,8,["src"])]}),_:1})]),_:1}),d(l,{class:"description-container"},{default:r(()=>[d(l,{class:"swiper-container"},{default:r(()=>[d(O,{class:"description-swiper",autoplay:"",interval:"3000"},{default:r(()=>{var e;return[(u(!0),_(v,null,y(null==(e=z.value)?void 0:e.role.partner.description,e=>(u(),n(i,{key:e},{default:r(()=>[d(l,{class:"desc-item"},{default:r(()=>[d(s,{class:"desc-text"},{default:r(()=>[p(b(e),1)]),_:2},1024)]),_:2},1024)]),_:2},1024))),128))]}),_:1})]),_:1})]),_:1})]),_:1})]),_:1}),d(l,{class:"attributes"},{default:r(()=>[d(s,{class:"attr-title"},{default:r(()=>[p("天空视图对比")]),_:1}),d(s,{class:"oracle-matched-title"},{default:r(()=>[p("“星灵”匹配度")]),_:1}),d(l,{class:"radar-comparison-container"},{default:r(()=>[(u(),_("svg",{class:"radar-comparison-chart",viewBox:"0 0 300 300"},[M("defs",null,[M("mask",{id:"aiRadarMask"},[M("rect",{width:"300",height:"300",fill:"white"}),M("path",{d:ae(),fill:"black"},null,8,["d"])])]),M("g",{class:"radar-grid"},[(u(!0),_(v,null,y(Q(),(e,a)=>(u(),_("path",{key:a,d:e,fill:"none",stroke:"rgba(255,255,255,0.2)","stroke-width":"1"},null,8,["d"]))),128))]),M("path",{d:ee(),fill:"rgba(255, 215, 0, 0.3)",stroke:"none",mask:"url(#aiRadarMask)",class:"radar-path-animate"},null,8,["d"]),M("path",{d:ee(),fill:"none",stroke:"#FFD700","stroke-width":"2",class:"radar-path-animate radar-glow-gold"},null,8,["d"]),M("path",{d:ae(),fill:"rgba(33, 150, 243, 0.3)",stroke:"#2196f3","stroke-width":"2",class:"radar-path-animate radar-glow-blue"},null,8,["d"])])),d(l,{class:"radar-comparison-labels"},{default:r(()=>[(u(!0),_(v,null,y(q.value,(e,a)=>(u(),n(l,{key:a,class:"radar-comparison-label",style:I({left:150+120*Math.cos((60*a-90)*Math.PI/180)+"px",top:150+120*Math.sin((60*a-90)*Math.PI/180)+"px"})},{default:r(()=>[d(s,{class:"label-text"},{default:r(()=>[p(b(e.label_zh),1)]),_:2},1024)]),_:2},1032,["style"]))),128))]),_:1}),d(l,{class:"radar-comparison-legend"},{default:r(()=>[d(l,{class:"legend-item"},{default:r(()=>[d(l,{class:"legend-color",style:{"background-color":"#FFD700"}}),d(s,{class:"legend-label"},{default:r(()=>[p("星灵")]),_:1})]),_:1}),d(l,{class:"legend-item"},{default:r(()=>[d(l,{class:"legend-color",style:{"background-color":"#2196f3"}}),d(s,{class:"legend-label"},{default:r(()=>[p("你的")]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})]),_:1}),d(l,{class:"detail-footer"},{default:r(()=>[d(l,{class:"action-btn primary",onClick:a[2]||(a[2]=e=>Z(z.value))},{default:r(()=>[d(s,null,{default:r(()=>[p("前往位置")]),_:1})]),_:1}),d(l,{class:"action-btn secondary",onClick:W},{default:r(()=>[d(s,null,{default:r(()=>[p("关闭")]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})):h("",!0),d(l,{class:"bottom-indicator"},{default:r(()=>[d(l,{class:"nav-bar"})]),_:1})]),_:1})}}}),[["__scopeId","data-v-83ec4c4f"]]);export{A as default};
