import { http } from '@/utils/http'

// 更新用户昵称
export const updateNickname = (nickname: string) => {
  return http({
    method: 'POST',
      url:'/user/updateNickname',
      data: {
        nickname
      },
      header: {
        'content-type': 'application/x-www-form-urlencoded'
      }
  })
}

// 更新伙伴昵称
export const updatePartner = (nickname: string) => {
  return http({
    method: 'POST',
      url:'/partner/updateNickname',
      data: {
        nickname
      },
      header: {
        'content-type': 'application/x-www-form-urlencoded'
      }
  })
}

// 更新用户联系号码
export const updatePhone = (phone: string) => {
  return http({
    method: 'POST',
      url:'/user/updatePhone',
      data: {
        phone
      },
      header: {
        'content-type': 'application/x-www-form-urlencoded'
      }
  })
}