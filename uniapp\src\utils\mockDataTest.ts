// 模拟数据测试工具
import { mockExchangeData } from '@/mock/exchangeData'
import { mockRankingData } from '@/mock/rankingData'

// 测试社区数据
export const testExchangeData = () => {
  console.log('=== 社区交流数据测试 ===')
  
  // 测试各分类帖子数量
  Object.keys(mockExchangeData.posts).forEach(category => {
    const posts = mockExchangeData.posts[category]
    console.log(`${category} 分类: ${posts.length} 个帖子`)
  })
  
  // 测试评论数据
  console.log(`评论数据: ${Object.keys(mockExchangeData.comments).length} 个帖子有评论`)
  
  return true
}

// 测试排行榜数据
export const testRankingData = () => {
  console.log('=== 排行榜数据测试 ===')
  
  // 测试各维度数据
  Object.keys(mockRankingData).forEach(dimension => {
    const data = mockRankingData[dimension]
    console.log(`${dimension} 维度: ${data.list.length} 个用户, 我的排名: ${data.myRank}`)
  })
  
  return true
}

// 运行所有测试
export const runAllTests = () => {
  testExchangeData()
  testRankingData()
  console.log('=== 所有测试完成 ===')
}