import{d as a,af as e,A as t,I as s,c as n,w as r,ai as o,i as c,o as l,a as i,O as d,j as m,e as p,S as v,f as u,F as h,r as k,p as f,b as _,t as y,u as g,as as b,at as x,au as S}from"./index-C6kXbWaK.js";import{_ as R}from"./back.DIErNce1.js";import{_ as w}from"./_plugin-vue_export-helper.BCo6x5W8.js";const j={spontaneous:{list:[{id:1,nickname:"自由飞翔",avatar:"https://picsum.photos/100/100?random=31",level:15,score:2847,trend:3},{id:2,nickname:"随心所欲",avatar:"https://picsum.photos/100/100?random=32",level:14,score:2756,trend:-1},{id:3,nickname:"潇洒人生",avatar:"https://picsum.photos/100/100?random=33",level:13,score:2689,trend:2},{id:4,nickname:"无拘无束",avatar:"https://picsum.photos/100/100?random=34",level:12,score:2534,trend:0},{id:5,nickname:"天马行空",avatar:"https://picsum.photos/100/100?random=35",level:11,score:2456,trend:1},{id:6,nickname:"随性而为",avatar:"https://picsum.photos/100/100?random=36",level:10,score:2378,trend:-2},{id:7,nickname:"自然流露",avatar:"https://picsum.photos/100/100?random=37",level:9,score:2289,trend:1},{id:8,nickname:"率性真我",avatar:"https://picsum.photos/100/100?random=38",level:8,score:2156,trend:0},{id:9,nickname:"随遇而安",avatar:"https://picsum.photos/100/100?random=39",level:7,score:2034,trend:2},{id:10,nickname:"洒脱不羁",avatar:"https://picsum.photos/100/100?random=40",level:6,score:1967,trend:-1}],myRank:23,myScore:1456},collaborative:{list:[{id:11,nickname:"团队之星",avatar:"https://picsum.photos/100/100?random=41",level:16,score:3124,trend:2},{id:12,nickname:"合作达人",avatar:"https://picsum.photos/100/100?random=42",level:15,score:2987,trend:0},{id:13,nickname:"协同专家",avatar:"https://picsum.photos/100/100?random=43",level:14,score:2834,trend:1},{id:14,nickname:"沟通高手",avatar:"https://picsum.photos/100/100?random=44",level:13,score:2756,trend:-1},{id:15,nickname:"桥梁建造者",avatar:"https://picsum.photos/100/100?random=45",level:12,score:2689,trend:3},{id:16,nickname:"和谐使者",avatar:"https://picsum.photos/100/100?random=46",level:11,score:2567,trend:0},{id:17,nickname:"团结力量",avatar:"https://picsum.photos/100/100?random=47",level:10,score:2445,trend:2},{id:18,nickname:"共赢思维",avatar:"https://picsum.photos/100/100?random=48",level:9,score:2334,trend:-2},{id:19,nickname:"协作精神",avatar:"https://picsum.photos/100/100?random=49",level:8,score:2223,trend:1},{id:20,nickname:"集体智慧",avatar:"https://picsum.photos/100/100?random=50",level:7,score:2112,trend:0}],myRank:18,myScore:1789},realist:{list:[{id:21,nickname:"务实先锋",avatar:"https://picsum.photos/100/100?random=51",level:17,score:3456,trend:1},{id:22,nickname:"脚踏实地",avatar:"https://picsum.photos/100/100?random=52",level:16,score:3234,trend:0},{id:23,nickname:"现实主义者",avatar:"https://picsum.photos/100/100?random=53",level:15,score:3089,trend:2},{id:24,nickname:"实用至上",avatar:"https://picsum.photos/100/100?random=54",level:14,score:2945,trend:-1},{id:25,nickname:"理性判断",avatar:"https://picsum.photos/100/100?random=55",level:13,score:2823,trend:1},{id:26,nickname:"客观分析",avatar:"https://picsum.photos/100/100?random=56",level:12,score:2701,trend:0},{id:27,nickname:"实际行动",avatar:"https://picsum.photos/100/100?random=57",level:11,score:2589,trend:3},{id:28,nickname:"现实考量",avatar:"https://picsum.photos/100/100?random=58",level:10,score:2467,trend:-2},{id:29,nickname:"务实态度",avatar:"https://picsum.photos/100/100?random=59",level:9,score:2345,trend:1},{id:30,nickname:"实事求是",avatar:"https://picsum.photos/100/100?random=60",level:8,score:2234,trend:0}],myRank:15,myScore:1923},logical:{list:[{id:31,nickname:"逻辑大师",avatar:"https://picsum.photos/100/100?random=61",level:18,score:3789,trend:2},{id:32,nickname:"推理专家",avatar:"https://picsum.photos/100/100?random=62",level:17,score:3567,trend:0},{id:33,nickname:"思维清晰",avatar:"https://picsum.photos/100/100?random=63",level:16,score:3345,trend:1},{id:34,nickname:"条理分明",avatar:"https://picsum.photos/100/100?random=64",level:15,score:3123,trend:-1},{id:35,nickname:"严密推论",avatar:"https://picsum.photos/100/100?random=65",level:14,score:2901,trend:2},{id:36,nickname:"理性思考",avatar:"https://picsum.photos/100/100?random=66",level:13,score:2789,trend:0},{id:37,nickname:"逻辑链条",avatar:"https://picsum.photos/100/100?random=67",level:12,score:2667,trend:1},{id:38,nickname:"因果关系",avatar:"https://picsum.photos/100/100?random=68",level:11,score:2545,trend:-2},{id:39,nickname:"演绎推理",avatar:"https://picsum.photos/100/100?random=69",level:10,score:2423,trend:3},{id:40,nickname:"归纳总结",avatar:"https://picsum.photos/100/100?random=70",level:9,score:2301,trend:0}],myRank:12,myScore:2156},analytical:{list:[{id:41,nickname:"数据分析师",avatar:"https://picsum.photos/100/100?random=71",level:19,score:4123,trend:1},{id:42,nickname:"深度解析",avatar:"https://picsum.photos/100/100?random=72",level:18,score:3901,trend:2},{id:43,nickname:"细致入微",avatar:"https://picsum.photos/100/100?random=73",level:17,score:3789,trend:0},{id:44,nickname:"洞察秋毫",avatar:"https://picsum.photos/100/100?random=74",level:16,score:3567,trend:-1},{id:45,nickname:"系统分析",avatar:"https://picsum.photos/100/100?random=75",level:15,score:3345,trend:1},{id:46,nickname:"结构化思维",avatar:"https://picsum.photos/100/100?random=76",level:14,score:3223,trend:0},{id:47,nickname:"多维度思考",avatar:"https://picsum.photos/100/100?random=77",level:13,score:3101,trend:2},{id:48,nickname:"量化分析",avatar:"https://picsum.photos/100/100?random=78",level:12,score:2989,trend:-1},{id:49,nickname:"模式识别",avatar:"https://picsum.photos/100/100?random=79",level:11,score:2867,trend:1},{id:50,nickname:"趋势预测",avatar:"https://picsum.photos/100/100?random=80",level:10,score:2745,trend:0}],myRank:8,myScore:2534},introvert:{list:[{id:51,nickname:"静水深流",avatar:"https://picsum.photos/100/100?random=81",level:14,score:2987,trend:1},{id:52,nickname:"内心世界",avatar:"https://picsum.photos/100/100?random=82",level:13,score:2834,trend:0},{id:53,nickname:"深度思考者",avatar:"https://picsum.photos/100/100?random=83",level:12,score:2712,trend:2},{id:54,nickname:"独处时光",avatar:"https://picsum.photos/100/100?random=84",level:11,score:2589,trend:-1},{id:55,nickname:"安静力量",avatar:"https://picsum.photos/100/100?random=85",level:10,score:2467,trend:1},{id:56,nickname:"内敛智慧",avatar:"https://picsum.photos/100/100?random=86",level:9,score:2345,trend:0},{id:57,nickname:"沉默是金",avatar:"https://picsum.photos/100/100?random=87",level:8,score:2223,trend:3},{id:58,nickname:"内省修养",avatar:"https://picsum.photos/100/100?random=88",level:7,score:2101,trend:-2},{id:59,nickname:"宁静致远",avatar:"https://picsum.photos/100/100?random=89",level:6,score:1989,trend:1},{id:60,nickname:"独立思维",avatar:"https://picsum.photos/100/100?random=90",level:5,score:1867,trend:0}],myRank:27,myScore:1234}},C=w(a({__name:"ranking",setup(a){const w=e(),C=t([{name:"随性",icon:"🚀",key:"spontaneous"},{name:"协作",icon:"🤝",key:"collaborative"},{name:"现实",icon:"🎯",key:"realist"},{name:"逻辑",icon:"🧠",key:"logical"},{name:"分析",icon:"📊",key:"analytical"},{name:"内向",icon:"🤔",key:"introvert"}]),I=t(0),q=t([]),A=t(!1),F=t(0),L=t(0),O=a=>["🥇","🥈","🥉"][a],P=async()=>{A.value=!0;try{const a=C.value[I.value];await new Promise(a=>setTimeout(a,500));const e=j[a.key];q.value=e.list||[],F.value=e.myRank||0,L.value=e.myScore||0}catch(a){console.error("加载排行榜失败:",a),o({title:"加载失败",icon:"none"})}finally{A.value=!1}},T=()=>{1==b().length?x({url:"/pages/ai/ai"}):S({delta:1})};return s(()=>{P()}),(a,e)=>{const t=m,s=c,o=_,b=v;return l(),n(s,{class:"ranking-container"},{default:r(()=>[i(s,{class:"header"},{default:r(()=>[i(s,{class:"header-left",onClick:T},{default:r(()=>[i(t,{src:R,class:"back-icon"})]),_:1}),i(s,{class:"header-title"},{default:r(()=>[p("排行榜")]),_:1}),i(s,{class:"header-right"})]),_:1}),i(s,{class:"dimension-tabs"},{default:r(()=>[i(b,{"scroll-x":"true",class:"tabs-scroll"},{default:r(()=>[i(s,{class:"tabs-container"},{default:r(()=>[(l(!0),u(h,null,k(C.value,(a,e)=>(l(),n(s,{key:e,class:f(["tab-item",{active:I.value===e}]),onClick:a=>(a=>{I.value=a,P()})(e)},{default:r(()=>[i(o,{class:"tab-icon"},{default:r(()=>[p(y(a.icon),1)]),_:2},1024),i(o,{class:"tab-text"},{default:r(()=>[p(y(a.name),1)]),_:2},1024)]),_:2},1032,["class","onClick"]))),128))]),_:1})]),_:1})]),_:1}),g(w).profile?(l(),n(s,{key:0,class:"my-rank-card"},{default:r(()=>[i(s,{class:"my-rank-content"},{default:r(()=>[i(s,{class:"rank-info"},{default:r(()=>[i(o,{class:"my-rank-text"},{default:r(()=>[p("我的排名")]),_:1}),i(o,{class:"my-rank-number"},{default:r(()=>[p("🏆 "+y(F.value),1)]),_:1})]),_:1}),i(s,{class:"user-info"},{default:r(()=>[i(t,{src:g(w).profile.avatar,class:"user-avatar"},null,8,["src"]),i(s,{class:"user-details"},{default:r(()=>[i(o,{class:"user-name"},{default:r(()=>[p(y(g(w).profile.nickname),1)]),_:1}),i(o,{class:"user-score"},{default:r(()=>[p(y(L.value)+"分",1)]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})):d("",!0),i(s,{class:"ranking-list"},{default:r(()=>[(l(!0),u(h,null,k(q.value,(a,e)=>(l(),n(s,{key:a.id,class:f(["ranking-item",{"top-three":e<3}])},{default:r(()=>[i(s,{class:"rank-number"},{default:r(()=>[e<3?(l(),n(s,{key:0,class:"medal"},{default:r(()=>[i(o,{class:"medal-icon"},{default:r(()=>[p(y(O(e)),1)]),_:2},1024)]),_:2},1024)):(l(),n(o,{key:1,class:"rank-text"},{default:r(()=>[p(y(e+1),1)]),_:2},1024))]),_:2},1024),i(s,{class:"user-section"},{default:r(()=>[i(t,{src:a.avatar,class:"avatar"},null,8,["src"]),i(s,{class:"user-info"},{default:r(()=>[i(o,{class:"username"},{default:r(()=>[p(y(a.nickname),1)]),_:2},1024),i(o,{class:"user-level"},{default:r(()=>[p("Lv."+y(a.level),1)]),_:2},1024)]),_:2},1024)]),_:2},1024),i(s,{class:"trend-section"},{default:r(()=>[i(t,{src:a.trend>0?"/static/ai/up.png":a.trend<0?"/static/ai/down.png":"/static/ai/equal.png",class:"trend-icon"},null,8,["src"]),i(o,{class:f(["trend-text",{"trend-up":a.trend>0,"trend-down":a.trend<0}])},{default:r(()=>[p(y(a.trend>0?"+"+a.trend:a.trend),1)]),_:2},1032,["class"])]),_:2},1024),i(s,{class:"score-section"},{default:r(()=>[i(o,{class:"score"},{default:r(()=>[p(y(a.score),1)]),_:2},1024),i(o,{class:"score-unit"})]),_:2},1024)]),_:2},1032,["class"]))),128))]),_:1}),A.value?(l(),n(s,{key:1,class:"loading"},{default:r(()=>[i(o,null,{default:r(()=>[p("加载中...")]),_:1})]),_:1})):d("",!0),A.value||0!==q.value.length?d("",!0):(l(),n(s,{key:2,class:"empty-state"},{default:r(()=>[i(o,{class:"empty-text"},{default:r(()=>[p("暂无排行数据")]),_:1})]),_:1}))]),_:1})}}}),[["__scopeId","data-v-c043ba91"]]);export{C as default};
