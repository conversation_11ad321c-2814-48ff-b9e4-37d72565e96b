<script setup lang="ts">
import { ref, onMounted, getCurrentInstance, computed } from 'vue'
import { useUserStore } from '@/stores/index'

const innerAudioContext = getCurrentInstance()?.appContext.config.globalProperties.$innerAudioContext
const userStore = useUserStore()

onMounted(() => {
  innerAudioContext.loop = true;
  innerAudioContext.autoplay = true;
    innerAudioContext.src = '/static/music/bg.mp3';
    innerAudioContext.onPlay(() => {
      console.log('开始播放');
    });
    innerAudioContext.onError((res) => {
      console.log(res.errMsg);
      console.log(res.errCode);
    });

  // 延迟初始化，确保Cesium已加载
  setTimeout(() => {
    initCesium();
  }, 100);
})

// 声明全局Cesium类型
declare global {
  const Cesium: any
}

let viewer: any;

// AI伴侣角色数据接口
interface AICompanion {
  id: string,
  avatar: string,
  nickname: string
  style: string
  createTime: string,
  role: {
    traits: string[],
    description: string,
    attribute: {
      spontaneous: number
      collaborative: number
      realist: number
      logical: number
      analytical: number
      introvert: number
    },
    partner: {
      nickName: string,
      style: string,
      createTime: string,
      traits: string[],
      description: string[],
      attribute: {
        spontaneous: number
        collaborative: number
        realist: number
        logical: number
        analytical: number
        introvert: number
      }
    }
  },
  position: {
    longitude: number
    latitude: number
    height: number
  },
  coreDimension: string
}

// 计算核心维度的函数
const getCoreDimension = (attributes: any): string => {
  const dimensionNames = {
    spontaneous: '随性',
    collaborative: '协作', 
    realist: '务实',
    logical: '逻辑',
    analytical: '分析',
    introvert: '内向'
  }
  
  let maxValue = 0
  let coreDimension = '随性'
  
  Object.entries(attributes).forEach(([key, value]) => {
    if (value > maxValue) {
      maxValue = value
      coreDimension = dimensionNames[key as keyof typeof dimensionNames] || '随性'
    }
  })
  
  return coreDimension
}

// 生成符合总和为96的6个维度属性
const generateBalancedAttributes = () => {
  const attributes = {
    spontaneous: 0,
    collaborative: 0,
    realist: 0,
    logical: 0,
    analytical: 0,
    introvert: 0
  }
  
  // 先给每个维度分配最小值1-8
  const keys = Object.keys(attributes) as (keyof typeof attributes)[]
  keys.forEach(key => {
    attributes[key] = Math.floor(Math.random() * 8) + 1 // 1-8
  })
  
  // 计算当前总和
  let currentSum = Object.values(attributes).reduce((sum, val) => sum + val, 0)
  
  // 分配剩余的点数到96
  let remaining = 96 - currentSum
  
  while (remaining > 0) {
    const randomKey = keys[Math.floor(Math.random() * keys.length)]
    if (attributes[randomKey] < 32) { // 确保不超过32
      const addValue = Math.min(remaining, 32 - attributes[randomKey], Math.floor(Math.random() * 5) + 1)
      attributes[randomKey] += addValue
      remaining -= addValue
    }
  }
  
  return attributes
}

// 生成随机AI伴侣数据的函数
const generateRandomCompanions = (): AICompanion[] => {
  const nicknames = [
    '小雪', '阿尔法', '小萌', '墨轩', '星辰', '月影', '晨曦', '夜语', '清风', '雨露',
    '紫霞', '青云', '白雪', '红叶', '金辉', '银月', '翠竹', '梅花', '兰草', '菊香',
    '桃花', '柳絮', '荷香', '桂花', '梧桐', '松涛', '竹韵', '花语', '鸟鸣', '蝶舞',
    '鱼跃', '龙吟', '凤鸣', '虎啸', '狼嚎', '鹰翔', '燕飞', '雁归', '鹤舞', '孔雀',
    '天使', '精灵', '仙子', '女神', '公主', '王子', '骑士', '法师', '战士', '弓手',
    '诗人', '画家', '音乐', '舞者', '歌手', '作家', '学者', '智者', '贤者', '圣者',
    '光明', '黑暗', '火焰', '冰霜', '雷电', '大地', '海洋', '天空', '星空', '彩虹',
    '梦境', '幻想', '现实', '未来', '过去', '永恒', '瞬间', '无限', '有限', '完美',
    '纯真', '善良', '勇敢', '智慧', '美丽', '优雅', '神秘', '可爱', '温柔', '坚强',
    '自由', '和平', '希望', '信念', '爱心', '友谊', '快乐', '幸福', '成功', '胜利'
  ]

  const role_traits = [
    ["好奇", "勇敢", "创新"], ["理性", "睿智", "逻辑"], ["活泼", "可爱", "开朗"], ["文艺", "内敛", "优雅"],
    ["乐观", "阳光", "热情"], ["沉稳", "内敛", "冷静"], ["幽默", "风趣", "机智"], ["神秘", "优雅", "高贵"],
    ["勇敢", "坚强", "无畏"], ["善良", "纯真", "温柔"], ["聪明", "机智", "敏锐"], ["浪漫", "多情", "感性"],
    ["冷静", "理智", "客观"], ["热情", "奔放", "活力"], ["温文", "尔雅", "绅士"], ["古灵", "精怪", "调皮"],
    ["成熟", "稳重", "可靠"], ["天真", "烂漫", "纯洁"], ["独立", "自主", "坚定"], ["细心", "体贴", "温暖"],
    ["创意", "无限", "想象"], ["逻辑", "清晰", "严谨"], ["感性", "丰富", "细腻"], ["理想", "主义", "追求"],
    ["现实", "主义", "务实"], ["完美", "主义", "精致"], ["自由", "奔放", "洒脱"], ["传统", "保守", "稳健"],
    ["前卫", "时尚", "潮流"], ["朴实", "无华", "真诚"]
  ]

  const partner_nicknames = [
    '小雪', '阿尔法', '小萌', '墨轩', '星辰', '月影', '晨曦', '夜语', '清风', '雨露',
    '紫霞', '青云', '白雪', '红叶', '金辉', '银月', '翠竹', '梅花', '兰草', '菊香',
    '桃花', '柳絮', '荷香', '桂花', '梧桐', '松涛', '竹韵', '花语', '鸟鸣', '蝶舞'
  ]

  const partner_traits = [
    ["好奇", "勇敢", "创新"], ["理性", "睿智", "逻辑"], ["活泼", "可爱", "开朗"], ["文艺", "内敛", "优雅"],
    ["乐观", "阳光", "热情"], ["沉稳", "内敛", "冷静"], ["幽默", "风趣", "机智"], ["神秘", "优雅", "高贵"],
    ["勇敢", "坚强", "无畏"], ["善良", "纯真", "温柔"], ["聪明", "机智", "敏锐"], ["浪漫", "多情", "感性"],
    ["冷静", "理智", "客观"], ["热情", "奔放", "活力"], ["温文", "尔雅", "绅士"], ["古灵", "精怪", "调皮"],
    ["成熟", "稳重", "可靠"], ["天真", "烂漫", "纯洁"], ["独立", "自主", "坚定"], ["细心", "体贴", "温暖"],
    ["创意", "无限", "想象"], ["逻辑", "清晰", "严谨"], ["感性", "丰富", "细腻"], ["理想", "主义", "追求"],
    ["现实", "主义", "务实"], ["完美", "主义", "精致"], ["自由", "奔放", "洒脱"], ["传统", "保守", "稳健"],
    ["前卫", "时尚", "潮流"], ["朴实", "无华", "真诚"]
  ]

  const avatars = [
    '/static/style/introvert.png', '/static/test1.png', '/static/style/realist.png',
    '/static/style/logical.png', '/static/style/analytical.png', '/static/style/collaborative.png', '/static/style/spontaneous.png',
  ]

  const role_descriptions = [
    "你总是充满热情，善于与人交流，总是能理解他人的感受。",
    "拥有超强逻辑思维，擅长解决复杂问题。",
    "充满活力，总能带来欢声笑语。",
    "富有艺术气息，喜欢诗词歌赋。",
    "阳光开朗，积极向上。",
    "沉稳内敛，深思熟虑。",
    "幽默风趣，机智过人。",
    "神秘优雅，气质非凡。",
    "勇敢坚强，面对困难从不退缩。",
    "善良纯真，心地善良。",
    "聪明机智，思维敏捷。",
    "浪漫多情，感情丰富。",
    "冷静理智，客观公正。",
    "热情奔放，充满活力。",
    "温文尔雅，举止得体。",
    "古灵精怪，充满创意。",
    "成熟稳重，值得信赖。",
    "天真烂漫，纯洁无瑕。",
    "独立自主，坚定不移。",
    "细心体贴，温暖如春。"
  ]

  const partner_descriptions = [
    ["你总是充满热情，善于与人交流，总是能理解他人的感受。", "你富有想象力，喜欢为生活增添色彩。", "你的存在就像阳光一样，总能给周围的人带来温暖和快乐。"],
    ["拥有超强逻辑思维，擅长解决复杂问题。", "善于理性分析，提供客观建议。", "是你最可靠的智慧伙伴。"],
    ["充满活力，总能带来欢声笑语。", "让生活充满乐趣和惊喜。", "用快乐感染身边的每一个人。"],
    ["富有艺术气息，喜欢诗词歌赋。", "能够提供深度的精神交流。", "用美好的文字温暖心灵。"],
    ["阳光开朗，积极向上。", "总是能看到事物美好的一面。", "用乐观的态度面对一切挑战。"],
    ["沉稳内敛，深思熟虑。", "在喧嚣中保持内心的宁静。", "用智慧和耐心解决问题。"],
    ["幽默风趣，机智过人。", "总能在适当的时候调节气氛。", "用笑声化解生活中的烦恼。"],
    ["神秘优雅，气质非凡。", "总是能在关键时刻给出最合适的建议。", "用独特的魅力吸引着他人。"],
    ["勇敢坚强，面对困难从不退缩。", "是你最可靠的伙伴。", "用坚定的意志克服一切障碍。"],
    ["善良纯真，心地善良。", "用真诚和善意对待每一个人。", "是这个世界上最美好的存在。"],
    ["聪明机智，思维敏捷。", "善于思考和分析，是你最好的智囊团。", "用智慧照亮前进的道路。"],
    ["浪漫多情，感情丰富。", "用细腻的情感体验生活的美好。", "是最懂得爱与被爱的存在。"],
    ["冷静理智，客观公正。", "在复杂的情况下保持清醒的头脑。", "用理性的思维做出最佳决策。"],
    ["热情奔放，充满活力。", "用激情点燃生活的每一个瞬间。", "是最有感染力的存在。"],
    ["温文尔雅，举止得体。", "用优雅的姿态面对生活。", "是最有修养的绅士淑女。"],
    ["古灵精怪，充满创意。", "总能想出意想不到的点子。", "用独特的思维方式看待世界。"],
    ["成熟稳重，值得信赖。", "在关键时刻总能挺身而出。", "是最可靠的人生导师。"],
    ["天真烂漫，纯洁无瑕。", "用最纯真的心灵感受世界。", "是这个世界上最珍贵的存在。"],
    ["独立自主，坚定不移。", "有着明确的目标和方向。", "用自己的力量创造美好的未来。"],
    ["细心体贴，温暖如春。", "总是能察觉到他人的需要。", "用关怀和爱心温暖着每一个人。"]
  ]
  
  const companions: AICompanion[] = []
  
  // 生成100个新的AI伴侣（在10公里范围内）
  // 10公里约等于0.09度（纬度）和0.11度（经度，在23度纬度处）
  const baseLatitude = 23.73
  const baseLongitude = 113.09
  const latRange = 0.09 // 约10公里
  const lonRange = 0.11 // 约10公里
  
  for (let i = 5; i <= 104; i++) {
    // 随机生成位置（在10公里范围内）
    const randomLat = baseLatitude + (Math.random() - 0.5) * latRange
    const randomLon = baseLongitude + (Math.random() - 0.5) * lonRange
    
    // 生成role的6个维度属性（总和为96）
    const roleAttributes = generateBalancedAttributes()
    
    // 生成partner的6个维度属性（总和为96）
    const partnerAttributes = generateBalancedAttributes()
    
    // 随机选择特质和描述
    const randomRoleTraitIndex = Math.floor(Math.random() * role_traits.length)
    const randomRoleDescIndex = Math.floor(Math.random() * role_descriptions.length)
    const randomPartnerTraitIndex = Math.floor(Math.random() * partner_traits.length)
    const randomPartnerDescIndex = Math.floor(Math.random() * partner_descriptions.length)

    const currentTime = new Date().toISOString()

    companions.push({
      id: i.toString(),
      avatar: avatars[Math.floor(Math.random() * avatars.length)],
      nickname: nicknames[Math.floor(Math.random() * nicknames.length)],
      style: avatars[Math.floor(Math.random() * avatars.length)],
      createTime: currentTime,
      role: {
        traits: role_traits[randomRoleTraitIndex],
        description: role_descriptions[randomRoleDescIndex],
        attribute: roleAttributes,
        partner: {
          nickName: partner_nicknames[Math.floor(Math.random() * partner_nicknames.length)],
          style: avatars[Math.floor(Math.random() * avatars.length)],
          createTime: currentTime,
          traits: partner_traits[randomPartnerTraitIndex],
          description: partner_descriptions[randomPartnerDescIndex],
          attribute: partnerAttributes
        }
      },
      position: {
        longitude: randomLon,
        latitude: randomLat,
        height: 100
      },
      coreDimension: getCoreDimension(roleAttributes)
    })
  }
  
  return companions
}

// 模拟AI伴侣数据
const aiCompanions = ref<AICompanion[]>(generateRandomCompanions())

// 当前选中的AI伴侣
const selectedCompanion = ref<AICompanion | null>(null)
const showCompanionDetail = ref(false)
const showCompanionList = ref(false)

// 清远市坐标 (经度: 113.03, 纬度: 23.68)
const qingyuanCoordinates = {
  longitude: 113.09,
  latitude: 23.73,
  height: 10000 // 高度10公里，适合俯瞰城市
}

// 跳转到清远市
const flyToQingyuan = () => {
  if (viewer) {
    viewer.camera.flyTo({
      destination: Cesium.Cartesian3.fromDegrees(
        qingyuanCoordinates.longitude, 
        qingyuanCoordinates.latitude, 
        qingyuanCoordinates.height
      ),
      duration: 3.0, // 飞行时间3秒
      complete: () => {
        uni.showToast({
          title: '欢迎来到清远大学城',
          icon: 'none'
        })
      }
    })
  }
}

// 处理点击事件
const setupClickHandler = () => {
  if (!viewer) return
  
  viewer.cesiumWidget.screenSpaceEventHandler.setInputAction((event: any) => {
    const pickedObject = viewer.scene.pick(event.position)
    
    if (Cesium.defined(pickedObject) && pickedObject.id && pickedObject.id.properties) {
      const companionData = pickedObject.id.properties.companionData
      if (companionData) {
        selectedCompanion.value = companionData._value || companionData
        showCompanionDetail.value = true
      }
    }
  }, Cesium.ScreenSpaceEventType.LEFT_CLICK)
}

// 初始化Cesium地图
const initCesium = () => {
  try {
    // 检查Cesium是否已加载
    if (typeof Cesium === 'undefined') {
      console.error('Cesium未正确加载')
      uni.showToast({
        title: 'Cesium未正确加载',
        icon: 'none'
      })
      return
    }
    
    // 设置Cesium的基本配置
    Cesium.Ion.defaultAccessToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiI2ZDY5NGRkMC04MThiLTRjYjYtYTkxOC1kYmU5ZGY5OGZmODAiLCJpZCI6MzIyMjQ3LCJpYXQiOjE3NTI3MjM3OTF9.IoUXBk4QpY_Mb--OsZIKzRXGFabLlugCOmCKJuNnwKA'
    
    const cesiumConfig = {
      // 主页按钮
      homeButton: false,
      // 场景模式选择器
      sceneModePicker: false,
      // 全屏按钮
      fullscreenButton: false,
      // 是否显示点击要素之后显示的信息
      infoBox: false,
      // 要素选中框
      selectionIndicator: false,
      // 影像切换
      baseLayerPicker: false,
      // 启用了阴影效果
      shadows: true,
      // 启用动画
      shouldAnimate: true,
      // 是否显示动画控件
      animation: false,
      // 是否显示时间线控件
      timeline: false,
      // 是否显示地名查找控件
      geocoder: false,
      // 是否显示帮助信息控件
      navigationHelpButton: false,
      // 版权信息
      creditContainer: document.createElement('div')
    }
    
    viewer = new Cesium.Viewer('cesiumContainer', cesiumConfig)
    
    // 设置点击事件处理
    setupClickHandler()
    
    // 地图加载完成后自动跳转到清远市并添加标记
    setTimeout(() => {
      flyToQingyuan()
      addCompanionMarkers()
      addSnowScene() // 添加雪地场景
    }, 1000)
    
  } catch (error) {
    console.error('Cesium初始化失败:', error)
    uni.showToast({
      title: 'Cesium初始化失败',
      icon: 'none'
    })
  }
}

// 关闭详情弹窗
const closeCompanionDetail = () => {
  showCompanionDetail.value = false
  selectedCompanion.value = null
}

// 飞到指定AI伴侣位置
const flyToCompanion = (companion: AICompanion) => {
  if (viewer) {
    viewer.camera.flyTo({
      destination: Cesium.Cartesian3.fromDegrees(
        companion.position.longitude,
        companion.position.latitude,
        1000 // 较低高度以便查看
      ),
      duration: 2.0
    })
  }
}

// 创建雪花纹理
const createSnowflakeTexture = () => {
  const canvas = document.createElement('canvas')
  canvas.width = 32
  canvas.height = 32
  const ctx = canvas.getContext('2d')
  
  // 绘制雪花
  ctx.fillStyle = 'white'
  ctx.shadowColor = 'rgba(255, 255, 255, 0.8)'
  ctx.shadowBlur = 4
  
  // 绘制六角雪花
  const centerX = 16
  const centerY = 16
  const radius = 12
  
  ctx.beginPath()
  for (let i = 0; i < 6; i++) {
    const angle = (i * Math.PI) / 3
    const x1 = centerX + Math.cos(angle) * radius
    const y1 = centerY + Math.sin(angle) * radius
    const x2 = centerX + Math.cos(angle) * (radius * 0.3)
    const y2 = centerY + Math.sin(angle) * (radius * 0.3)
    
    ctx.moveTo(centerX, centerY)
    ctx.lineTo(x1, y1)
    ctx.moveTo(x2, y2)
    ctx.lineTo(centerX + Math.cos(angle + Math.PI/6) * (radius * 0.6), 
               centerY + Math.sin(angle + Math.PI/6) * (radius * 0.6))
    ctx.moveTo(x2, y2)
    ctx.lineTo(centerX + Math.cos(angle - Math.PI/6) * (radius * 0.6), 
               centerY + Math.sin(angle - Math.PI/6) * (radius * 0.6))
  }
  ctx.strokeStyle = 'white'
  ctx.lineWidth = 2
  ctx.stroke()
  
  // 中心点
  ctx.beginPath()
  ctx.arc(centerX, centerY, 3, 0, 2 * Math.PI)
  ctx.fill()
  
  return canvas.toDataURL()
}

// 添加雪地场景效果
const addSnowScene = () => {
  if (!viewer) return
  
  try {
    // 创建雪花纹理
    const snowflakeImage = createSnowflakeTexture()
    
    // 添加多层雪花粒子系统以增强效果
    for (let layer = 0; layer < 3; layer++) {
      const snowParticleSystem = viewer.scene.primitives.add(new Cesium.ParticleSystem({
        image: snowflakeImage,
        startColor: Cesium.Color.WHITE.withAlpha(0.9 - layer * 0.2),
        endColor: Cesium.Color.WHITE.withAlpha(0.1),
        startScale: 0.8 + layer * 0.3,
        endScale: 0.2,
        particleLife: 12.0 + layer * 3.0,
        speed: 3.0 + layer * 2.0,
        imageSize: new Cesium.Cartesian2(8.0 + layer * 4.0, 8.0 + layer * 4.0),
        emissionRate: 800.0 - layer * 200.0,
        lifetime: 20.0,
        emitter: new Cesium.BoxEmitter(new Cesium.Cartesian3(
          30000.0 + layer * 10000.0, 
          30000.0 + layer * 10000.0, 
          2000.0 + layer * 1000.0
        )),
        modelMatrix: Cesium.Transforms.eastNorthUpToFixedFrame(
          Cesium.Cartesian3.fromDegrees(
            qingyuanCoordinates.longitude, 
            qingyuanCoordinates.latitude, 
            3000 + layer * 1000
          )
        ),
        gravity: -2.8 - layer * 1.0,
        // 添加风力效果
        forces: [
          function(particle) {
            // 模拟风力
            const windForce = new Cesium.Cartesian3(
              Math.sin(Date.now() * 0.001 + layer) * 0.5,
              Math.cos(Date.now() * 0.001 + layer) * 0.3,
              0
            )
            return windForce
          }
        ]
      }))
    }
    
    // 增强环境光效和大气效果
    viewer.scene.globe.enableLighting = true
    viewer.scene.globe.dynamicAtmosphereLighting = true
    viewer.scene.globe.dynamicAtmosphereLightingFromSun = false
    
    // 调整大气效果以营造冬日氛围
    viewer.scene.skyAtmosphere.hueShift = -0.3
    viewer.scene.skyAtmosphere.saturationShift = -0.6
    viewer.scene.skyAtmosphere.brightnessShift = 0.2
    
    // 设置雾效
    viewer.scene.fog.enabled = true
    viewer.scene.fog.density = 0.0002
    viewer.scene.fog.screenSpaceErrorFactor = 2.0
    
    // 调整光照
    viewer.scene.light = new Cesium.DirectionalLight({
      direction: new Cesium.Cartesian3(0.2, 0.5, -0.8),
      color: new Cesium.Color(0.9, 0.95, 1.0, 1.0),
      intensity: 2.0
    })
    
    console.log('雪地场景添加成功')
    
  } catch (error) {
    console.error('添加雪地场景失败:', error)
  }
}

// 创建雪地纹理
const createSnowTexture = () => {
  const canvas = document.createElement('canvas')
  canvas.width = 128
  canvas.height = 128
  const ctx = canvas.getContext('2d')
  
  // 创建雪地纹理
  ctx.fillStyle = '#ffffff'
  ctx.fillRect(0, 0, 128, 128)
  
  // 添加雪地细节
  for (let i = 0; i < 200; i++) {
    const x = Math.random() * 128
    const y = Math.random() * 128
    const size = Math.random() * 3 + 1
    
    ctx.fillStyle = `rgba(240, 248, 255, ${Math.random() * 0.8 + 0.2})`
    ctx.beginPath()
    ctx.arc(x, y, size, 0, 2 * Math.PI)
    ctx.fill()
  }
  
  // 添加阴影效果
  for (let i = 0; i < 50; i++) {
    const x = Math.random() * 128
    const y = Math.random() * 128
    const size = Math.random() * 2 + 0.5
    
    ctx.fillStyle = `rgba(200, 220, 240, ${Math.random() * 0.3 + 0.1})`
    ctx.beginPath()
    ctx.arc(x, y, size, 0, 2 * Math.PI)
    ctx.fill()
  }
  
  return canvas.toDataURL()
}

// 添加AI伴侣标记到地图
const addCompanionMarkers = () => {
  if (!viewer) return
  
  aiCompanions.value.forEach(companion => {
    // 创建标记点
    const entity = viewer.entities.add({
      position: Cesium.Cartesian3.fromDegrees(
        companion.position.longitude,
        companion.position.latitude,
        companion.position.height
      ),
      // 添加头像图标
      billboard: {
        image: companion.avatar,
        width: 63,
        height: 100,
        scale: 1.0,
        verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
        // 添加边框效果
        color: Cesium.Color.WHITE,
        outlineColor: Cesium.Color.fromCssColorString('#667eea'),
        outlineWidth: 3,
        // 让图标始终面向相机
        alignedAxis: Cesium.Cartesian3.UNIT_Z,
        rotation: 0
      },
      // 添加昵称标签
      label: {
        text: companion.role.partner.nickName,
        font: '16pt sans-serif',
        fillColor: Cesium.Color.WHITE,
        outlineColor: Cesium.Color.BLACK,
        outlineWidth: 2,
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
        pixelOffset: new Cesium.Cartesian2(0, -100), // 标签显示在头像上方
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
        // 添加背景
        backgroundColor: Cesium.Color.fromAlpha(Cesium.Color.BLACK, 0.7),
        backgroundPadding: new Cesium.Cartesian2(8, 4),
        showBackground: true
      },
      // 存储伴侣数据用于点击事件
      properties: {
        companionData: companion
      }
    })
    
    // 添加核心维度标签
    viewer.entities.add({
      position: Cesium.Cartesian3.fromDegrees(
        companion.position.longitude,
        companion.position.latitude,
        companion.position.height
      ),
      label: {
        text: companion.coreDimension,
        font: '12pt sans-serif',
        fillColor: Cesium.Color.GOLD,
        outlineColor: Cesium.Color.BLACK,
        outlineWidth: 2,
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
        pixelOffset: new Cesium.Cartesian2(35, -60), // 核心维度标签显示在右上角
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
        backgroundColor: Cesium.Color.fromAlpha(Cesium.Color.PURPLE, 0.8),
        backgroundPadding: new Cesium.Cartesian2(6, 3),
        showBackground: true,
        scale: 0.8
      }
    })
  })
}

// 处理主要内容区域点击事件
const handleMainContentClick = () => {
  // 如果左侧面板是展开状态，则收缩它
  if (showCompanionList.value) {
    showCompanionList.value = false
  }
}

// 返回首页
const goHome = () => {
  if (getCurrentPages().length == 1) {
    uni.reLaunch({ url: '/pages/ai/ai' })
  } else {
    uni.navigateBack({ delta: 1})
  }
}

const toFriends = () => {
  uni.showToast({
    title: '查看好友功能开发中',
    icon: 'none'
  })
}

// 添加新星球
const addNewPlanet = () => {
  uni.showToast({
    title: '添加新星球功能开发中',
    icon: 'none'
  })
}

// 添加雪花堆积效果
const addSnowAccumulation = () => {
  if (!viewer) return
  
  try {
    // 在AI伴侣周围添加圆形雪堆（使用圆形避免椭圆问题）
    aiCompanions.value.forEach((companion, index) => {
      // 为每个AI伴侣添加脚下的雪堆
      viewer.entities.add({
        position: Cesium.Cartesian3.fromDegrees(
          companion.position.longitude,
          companion.position.latitude,
          0
        ),
        cylinder: {
          topRadius: 12.0,
          bottomRadius: 15.0,
          length: 2.0,
          material: Cesium.Color.WHITE.withAlpha(0.7),
          outline: false
        }
      })
      
      // 添加随机小雪堆
      for (let i = 0; i < 3; i++) {
        const offsetLon = (Math.random() - 0.5) * 0.001
        const offsetLat = (Math.random() - 0.5) * 0.001
        const radius = Math.random() * 5 + 3
        
        viewer.entities.add({
          position: Cesium.Cartesian3.fromDegrees(
            companion.position.longitude + offsetLon,
            companion.position.latitude + offsetLat,
            0
          ),
          cylinder: {
            topRadius: radius * 0.8,
            bottomRadius: radius,
            length: Math.random() * 1.5 + 0.5,
            material: Cesium.Color.WHITE.withAlpha(0.5),
            outline: false
          }
        })
      }
    })
    
    console.log('雪花堆积效果添加成功')
  } catch (error) {
    console.error('添加雪花堆积效果失败:', error)
  }
}

// 添加冬日云层效果
const addWinterClouds = () => {
  if (!viewer) return
  
  try {
    // 添加低空云层 - 使用圆柱体避免椭圆问题
    for (let i = 0; i < 5; i++) {
      const cloudLon = qingyuanCoordinates.longitude + (Math.random() - 0.5) * 0.05
      const cloudLat = qingyuanCoordinates.latitude + (Math.random() - 0.5) * 0.05
      const cloudHeight = 1000 + Math.random() * 2000
      const cloudRadius = 1500 + Math.random() * 2000
      
      viewer.entities.add({
        position: Cesium.Cartesian3.fromDegrees(cloudLon, cloudLat, cloudHeight),
        cylinder: {
          topRadius: cloudRadius,
          bottomRadius: cloudRadius * 1.2,
          length: 200,
          material: Cesium.Color.WHITE.withAlpha(0.3),
          outline: false
        }
      })
    }
    
    console.log('冬日云层效果添加成功')
  } catch (error) {
    console.error('添加冬日云层效果失败:', error)
  }
}

// 生成雷达图网格路径（6边形）
const getRadarGridPaths = () => {
  const centerX = 150
  const centerY = 150
  const maxRadius = 100
  const paths = []
  
  // 生成同心六边形网格线 - 只要3圈
  for (let level = 1; level <= 3; level++) {
    const radius = (maxRadius * level) / 3
    let path = ''
    
    for (let i = 0; i < 6; i++) {
      const angle = ((i * 60) - 90) * Math.PI / 180  // 从顶部开始
      const x = centerX + radius * Math.cos(angle)
      const y = centerY + radius * Math.sin(angle)
      
      if (i === 0) {
        path = `M ${x} ${y}`
      } else {
        path += ` L ${x} ${y}`
      }
    }
    path += ' Z'
    paths.push(path)
  }
  
  return paths
}

// 生成雷达图数据路径
const getRadarDataPath = (attributes: any) => {
  const centerX = 150
  const centerY = 150
  const maxRadius = 100

  const dimensionOrder = ['spontaneous', 'collaborative', 'realist', 'logical', 'analytical', 'introvert']

  let path = ''

  dimensionOrder.forEach((dimension, index) => {
    const angle = ((index * 60) - 90) * Math.PI / 180
    const radius = (attributes[dimension] / 32) * maxRadius
    const x = centerX + radius * Math.cos(angle)
    const y = centerY + radius * Math.sin(angle)

    if (index === 0) {
      path = `M ${x} ${y}`
    } else {
      path += ` L ${x} ${y}`
    }
  })

  path += ' Z'
  return path
}

// 生成大尺寸雷达图网格路径（6边形）
const getRadarGridPathsLarge = () => {
  const centerX = 200
  const centerY = 200
  const maxRadius = 140
  const paths = []

  // 生成同心六边形网格线 - 只要3圈
  for (let level = 1; level <= 3; level++) {
    const radius = (maxRadius * level) / 3
    let path = ''

    for (let i = 0; i < 6; i++) {
      const angle = ((i * 60) - 90) * Math.PI / 180  // 从顶部开始
      const x = centerX + radius * Math.cos(angle)
      const y = centerY + radius * Math.sin(angle)

      if (i === 0) {
        path = `M ${x} ${y}`
      } else {
        path += ` L ${x} ${y}`
      }
    }
    path += ' Z'
    paths.push(path)
  }

  return paths
}

// 生成大尺寸雷达图数据路径
const getRadarDataPathLarge = (attributes: any) => {
  const centerX = 200
  const centerY = 200
  const maxRadius = 140

  const dimensionOrder = ['spontaneous', 'collaborative', 'realist', 'logical', 'analytical', 'introvert']

  let path = ''

  dimensionOrder.forEach((dimension, index) => {
    const angle = ((index * 60) - 90) * Math.PI / 180
    const radius = (attributes[dimension] / 32) * maxRadius
    const x = centerX + radius * Math.cos(angle)
    const y = centerY + radius * Math.sin(angle)

    if (index === 0) {
      path = `M ${x} ${y}`
    } else {
      path += ` L ${x} ${y}`
    }
  })

  path += ' Z'
  return path
}

// 雷达图对比数据
const companionRadarData = computed(() => {
  // 如果没有用户数据，使用默认值
  if (!userStore.profile?.attribute) {
    return [
      { label: 'spontaneous', label_zh: '随性', skychart: 0, you: 0 },
      { label: 'collaborative', label_zh: '协作', skychart: 0, you: 0 },
      { label: 'realist', label_zh: '务实', skychart: 0, you: 0 },
      { label: 'logical', label_zh: '逻辑', skychart: 0, you: 0 },
      { label: 'analytical', label_zh: '分析', skychart: 0, you: 0 },
      { label: 'introvert', label_zh: '内向', skychart: 0, you: 0 }
    ]
  }

  const userAttr = userStore.profile?.attribute || {}
  const companionAttr = selectedCompanion.value?.role.attribute || {} as any

  return [
    { label: 'spontaneous', label_zh: '随性', skychart: companionAttr.spontaneous || 0, you: userAttr.spontaneous || 0 },
    { label: 'collaborative', label_zh: '协作', skychart: companionAttr.collaborative || 0, you: userAttr.collaborative || 0 },
    { label: 'realist', label_zh: '务实', skychart: companionAttr.realist || 0, you: userAttr.realist || 0 },
    { label: 'logical', label_zh: '逻辑', skychart: companionAttr.logical || 0, you: userAttr.logical || 0 },
    { label: 'analytical', label_zh: '分析', skychart: companionAttr.analytical || 0, you: userAttr.analytical || 0 },
    { label: 'introvert', label_zh: '内向', skychart: companionAttr.introvert || 0, you: userAttr.introvert || 0 }
  ]
})

// 生成雷达图网格路径（用于对比图）
const getCompanionGridPaths = () => {
  const centerX = 150
  const centerY = 150
  const maxRadius = 100
  const paths = []

  // 生成同心六边形网格线 - 只要3圈
  for (let level = 1; level <= 3; level++) {
    const radius = (maxRadius * level) / 3
    let path = ''

    for (let i = 0; i < 6; i++) {
      const angle = ((i * 60) - 90) * Math.PI / 180  // 从顶部开始
      const x = centerX + radius * Math.cos(angle)
      const y = centerY + radius * Math.sin(angle)

      if (i === 0) {
        path = `M ${x} ${y}`
      } else {
        path += ` L ${x} ${y}`
      }
    }
    path += ' Z'
    paths.push(path)
  }

  return paths
}

// 生成Skychart雷达图数据路径
const getSkychartRadarPath = () => {
  const centerX = 150
  const centerY = 150
  const maxRadius = 100

  let path = ''

  companionRadarData.value.forEach((item, index) => {
    const angle = ((index * 60) - 90) * Math.PI / 180
    const radius = (item.skychart / 32) * maxRadius
    const x = centerX + radius * Math.cos(angle)
    const y = centerY + radius * Math.sin(angle)

    if (index === 0) {
      path = `M ${x} ${y}`
    } else {
      path += ` L ${x} ${y}`
    }
  })

  path += ' Z'
  return path
}

// 生成You雷达图数据路径
const getYouRadarPath = () => {
  const centerX = 150
  const centerY = 150
  const maxRadius = 100

  let path = ''

  companionRadarData.value.forEach((item, index) => {
    const angle = ((index * 60) - 90) * Math.PI / 180
    const radius = (item.you / 32) * maxRadius
    const x = centerX + radius * Math.cos(angle)
    const y = centerY + radius * Math.sin(angle)

    if (index === 0) {
      path = `M ${x} ${y}`
    } else {
      path += ` L ${x} ${y}`
    }
  })

  path += ' Z'
  return path
}


</script>

<template>
  <view class="planet-container">

    <!-- 顶部导航 -->
    <view class="top-nav">
      <view class="nav-left">
        <view class="home-btn" @click="goHome">
          <image src="/static/ai/home.png" mode="aspectFit" />
        </view>
      </view>
      <view class="nav-center">
        <view class="qingyuan-btn" @click="flyToQingyuan">
          <text>清远大学城</text>
        </view>
      </view>
      <view class="nav-right">
        <view class="profile-btn" @click="toFriends">
          <image style="width: 60rpx; height: 60rpx;" src="/static/ai/friends.png" mode="aspectFit" />
        </view>
      </view>
    </view>

    <!-- 左侧AI伴侣列表 -->
    <view class="companion-list" :class="{ 'show': showCompanionList }">
      <view class="list-header">
        <text class="title">附近的星灵</text>
        <view class="close-btn" @click="showCompanionList = false">×</view>
      </view>
      <scroll-view class="list-content" scroll-y>
        <view 
          class="companion-item" 
          v-for="companion in aiCompanions" 
          :key="companion.id"
          @click="flyToCompanion(companion)"
        >
          <view class="character">
            <image :src="companion.role.partner.style" mode="aspectFill" />
            <view class="core-dimension">{{ companion.role.partner.coreDimension }}</view>
          </view>
          <view class="info">
            <text class="name">{{ companion.role.partner.nickName }}</text>
            <view class="traits-display">
              <view v-for="trait in companion.role.traits" :key="trait" class="trait-tag">
                <text class="trait-text">{{ trait }}</text>
              </view>
            </view>
            <text class="owner">降临: {{ new Date(companion.role.partner.createTime).toLocaleDateString() }}</text>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 左侧切换按钮 -->
    <view class="companion-toggle" @click="showCompanionList = !showCompanionList">
      <image src="/static/ai/list.png" mode="aspectFit" />
    </view>

    <!-- 主要内容区域 -->
    <view class="main-content" @click="handleMainContentClick">
        <!-- 地球 -->
        <div class="cesium" id="cesiumContainer" style="width: 100%;height: 100vh;"></div>
    </view>

    <!-- AI伴侣详情弹窗 -->
    <view class="companion-detail-modal" v-if="showCompanionDetail" @click="closeCompanionDetail">
      <view class="detail-content" @click.stop>
        <view class="detail-header">
          <view class="character-large floating-avatar">
            <image :src="selectedCompanion?.avatar" mode="aspectFill" />
            <view class="core-dimension-badge">{{ selectedCompanion?.coreDimension }}</view>
          </view>
          <view class="basic-info">
            <text class="name">{{ selectedCompanion?.nickname }}</text>
            <view class="traits-display">
              <view v-for="trait in selectedCompanion?.role.traits" :key="trait" class="trait-tag">
                <text class="trait-text">{{ trait }}</text>
              </view>
            </view>
            <text class="owner">降临: {{ selectedCompanion?.createTime ? new Date(selectedCompanion.createTime).toLocaleDateString() : '' }}</text>
          </view>
          <view class="close-btn" @click="closeCompanionDetail">×</view>
        </view>
        
        <view class="detail-body">
          <!-- Partner完整信息展示 -->
          <view class="partner-complete-section">
            <view class="partner-showcase-container">
              <text class="partner-name">{{ selectedCompanion?.role.partner.nickName }}</text>
              <view class="traits-display-enhanced">
                <view v-for="trait in selectedCompanion?.role.partner.traits" :key="trait" class="trait-tag-highlight partner-trait">
                  <text class="trait-text">{{ trait }}</text>
                </view>
              </view>

              <view class="partner-avatar-container">
                <view class="character-avatar floating-avatar-delayed">
                  <image :src="selectedCompanion?.role.partner.style" mode="widthFix" />
                </view>
              </view>

              <view class="description-container">
                <view class="swiper-container">
                  <swiper
                    class="description-swiper"
                    autoplay
                    interval="3000"
                  >
                    <swiper-item v-for="description in selectedCompanion?.role.partner.description" :key="description">
                      <view class="desc-item">
                        <text class="desc-text">{{ description }}</text>
                      </view>
                    </swiper-item>
                  </swiper>
                </view>
              </view>
            </view>
          </view>
          
          <view class="attributes">
            <text class="attr-title">天空视图对比</text>
            <text class="oracle-matched-title">“星灵”匹配度</text>
        <view class="radar-comparison-container">
          <svg class="radar-comparison-chart" viewBox="0 0 300 300">
            <!-- 定义遮罩 -->
            <defs>
              <mask id="aiRadarMask">
                <!-- 白色区域显示，黑色区域隐藏 -->
                <rect width="300" height="300" fill="white"/>
                <!-- 用黑色遮罩掉用户雷达区域 -->
                <path 
                  :d="getYouRadarPath()" 
                  fill="black"
                />
              </mask>
            </defs>
            
            <!-- 网格线 -->
            <g class="radar-grid">
              <path 
                v-for="(path, index) in getCompanionGridPaths()" 
                :key="index"
                :d="path" 
                fill="none" 
                stroke="rgba(255,255,255,0.2)" 
                stroke-width="1"
              />
            </g>
            
            <!-- Skychart雷达图填充 (使用遮罩，不显示与用户重叠的部分) -->
            <path
              :d="getSkychartRadarPath()"
              fill="rgba(255, 215, 0, 0.3)"
              stroke="none"
              mask="url(#aiRadarMask)"
              class="radar-path-animate"
            />

            <!-- Skychart雷达图边框 (不使用遮罩，始终显示金色边框) -->
            <path
              :d="getSkychartRadarPath()"
              fill="none"
              stroke="#FFD700"
              stroke-width="2"
              class="radar-path-animate radar-glow-gold"
            />

             <!-- You雷达图 (在最上层，完全显示) -->
             <path
               :d="getYouRadarPath()"
               fill="rgba(33, 150, 243, 0.3)"
               stroke="#2196f3"
               stroke-width="2"
               class="radar-path-animate radar-glow-blue"
             />
                     </svg>
           
           <!-- 雷达图标签 -->
           <view class="radar-comparison-labels">
             <view 
               v-for="(item, index) in companionRadarData" 
               :key="index"
               class="radar-comparison-label"
               :style="{
                 left: (150 + 120 * Math.cos(((index * 60) - 90) * Math.PI / 180)) + 'px',
                 top: (150 + 120 * Math.sin(((index * 60) - 90) * Math.PI / 180)) + 'px'
               }"
             >
               <text class="label-text">{{ item.label_zh }}</text>
             </view>
           </view>
           
           <!-- 图例 -->
           <view class="radar-comparison-legend">
             <view class="legend-item">
               <view class="legend-color" style="background-color: #FFD700;"></view>
               <text class="legend-label">星灵</text>
             </view>
             <view class="legend-item">
               <view class="legend-color" style="background-color: #2196f3;"></view>
               <text class="legend-label">你的</text>
             </view>
           </view>
          </view>
          </view>
        </view>
        
        <view class="detail-footer">
          <view class="action-btn primary" @click="flyToCompanion(selectedCompanion!)">
            <text>前往位置</text>
          </view>
          <view class="action-btn secondary" @click="closeCompanionDetail">
            <text>关闭</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部导航指示器 -->
    <view class="bottom-indicator">
      <view class="nav-bar"></view>
    </view>
  </view>
</template>

<style scoped lang="scss">
.planet-container {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  position: relative;
}

// 顶部导航
.top-nav {
  position: absolute;
  top: 60rpx;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 30rpx;
  z-index: 10;
  
  .nav-left, .nav-right {
    .home-btn, .profile-btn {
      width: 80rpx;
      height: 80rpx;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      
      image {
        width: 50rpx;
        height: 50rpx;
        filter: brightness(0) invert(1);
      }
    }
  }
  
  .nav-center {
    .qingyuan-btn {
      padding: 20rpx 40rpx;
      background: rgba(255, 255, 255, 0.2);
      border: 2rpx solid rgba(255, 255, 255, 0.3);
      border-radius: 50rpx;
      backdrop-filter: blur(10rpx);
      display: flex;
      align-items: center;
      justify-content: center;
      
      text {
        color: white;
        font-size: 28rpx;
        font-weight: 500;
      }
      
      &:active {
        background: rgba(255, 255, 255, 0.3);
        transform: scale(0.95);
      }
    }
  }
}

// 主要内容
.main-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100%;
}

// 底部导航指示器
.bottom-indicator {
  position: absolute;
  bottom: 40rpx;
  left: 50%;
  transform: translateX(-50%);
  
  .nav-bar {
    width: 300rpx;
    height: 8rpx;
    background: #fff;
    border-radius: 4rpx;
  }
}

// 左侧AI伴侣列表
.companion-list {
  position: absolute;
  top: 0;
  left: -600rpx;
  width: 600rpx;
  height: 100vh;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(20rpx);
  z-index: 20;
  transition: left 0.3s ease;
  
  &.show {
    left: 0;
  }
  
  .list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 60rpx 40rpx 30rpx;
    border-bottom: 1rpx solid rgba(255, 255, 255, 0.1);
    
    .title {
      color: white;
      font-size: 36rpx;
      font-weight: bold;
    }
    
    .close-btn {
      width: 60rpx;
      height: 60rpx;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.1);
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 40rpx;
    }
  }
  
  .list-content {
    height: calc(100vh - 200rpx);
    padding: 20rpx;
  }
  
  .companion-item {
    display: flex;
    align-items: center;
    padding: 30rpx;
    margin-bottom: 20rpx;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20rpx;
    backdrop-filter: blur(10rpx);
    
    &:active {
      background: rgba(255, 255, 255, 0.2);
      transform: scale(0.98);
    }
    
    .character {
      position: relative;
      width: 100rpx;
      height: 100rpx;
      margin-right: 30rpx;
      
      image {
        width: 100%;
        height: 100%;
        border-radius: 50%;
        border: 3rpx solid rgba(255, 255, 255, 0.3);
      }
      
      .core-dimension {
        position: absolute;
        bottom: -5rpx;
        right: -5rpx;
        background: linear-gradient(45deg, #9c27b0, #673ab7);
        color: white;
        font-size: 18rpx;
        padding: 4rpx 8rpx;
        border-radius: 10rpx;
        font-weight: bold;
        min-width: 40rpx;
        text-align: center;
      }
    }
    
    .info {
      flex: 1;
      display: flex;
      flex-direction: column;
      
      .name {
        color: white;
        font-size: 32rpx;
        font-weight: bold;
        margin-bottom: 8rpx;
      }
      
      .traits-display {
          display: flex;
          flex-wrap: wrap;
          gap: 10rpx;
          margin-bottom: 10rpx;
          
          .trait-tag {
            background: linear-gradient(45deg, #9c27b0, #673ab7);
            border: 1rpx solid rgba(156, 39, 176, 0.5);
            border-radius: 15rpx;
            padding: 6rpx 12rpx;
            
            .trait-text {
              color: rgba(255, 255, 255, 0.9);
              font-size: 22rpx;
            }
          }
        }
      
      .personality {
        color: rgba(255, 255, 255, 0.8);
        font-size: 24rpx;
        margin-bottom: 8rpx;
      }
      
      .owner {
        color: rgba(255, 255, 255, 0.6);
        font-size: 22rpx;
      }
    }
  }
}

// 左侧切换按钮
.companion-toggle {
  position: absolute;
  top: 50%;
  left: 30rpx;
  transform: translateY(-50%);
  width: 80rpx;
  height: 80rpx;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 15;
  backdrop-filter: blur(10rpx);
  
  image {
    width: 40rpx;
    height: 40rpx;
    filter: brightness(0) invert(1);
  }
  
  &:active {
    background: rgba(0, 0, 0, 0.8);
    transform: translateY(-50%) scale(0.95);
  }
}

// AI伴侣详情弹窗
.companion-detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
  
  .detail-content {
    width: 90%;
    max-width: 800rpx;
    max-height: 80vh;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    border-radius: 30rpx;
    backdrop-filter: blur(20rpx);
    border: 1rpx solid rgba(255, 255, 255, 0.2);
    overflow: hidden;
    
    .detail-header {
      display: flex;
      align-items: center;
      padding: 40rpx;
      border-bottom: 1rpx solid rgba(255, 255, 255, 0.1);
      position: relative;
      
      .character-large {
        position: relative;
        width: 120rpx;
        height: 120rpx;
        margin-right: 30rpx;
        
        image {
          width: 100%;
          height: 100%;
          border-radius: 50%;
          border: 4rpx solid rgba(255, 255, 255, 0.3);
        }
        
        .core-dimension-badge {
          position: absolute;
          bottom: -8rpx;
          right: -8rpx;
          background: linear-gradient(45deg, #9c27b0, #673ab7);
          color: white;
          font-size: 22rpx;
          padding: 6rpx 12rpx;
          border-radius: 15rpx;
          font-weight: bold;
          min-width: 50rpx;
          text-align: center;
        }
      }
      
      .basic-info {
        flex: 1;
        display: flex;
        flex-direction: column;
        
        .name {
          color: white;
          font-size: 40rpx;
          font-weight: bold;
          margin-bottom: 10rpx;
        }
        
        .personality {
          color: rgba(255, 255, 255, 0.8);
          font-size: 28rpx;
          margin-bottom: 8rpx;
        }
        
        .owner {
          color: rgba(255, 255, 255, 0.6);
          font-size: 24rpx;
        }
        
        .traits-display {
          display: flex;
          flex-wrap: wrap;
          gap: 10rpx;
          margin-bottom: 10rpx;
          
          .trait-tag {
            background: linear-gradient(45deg, #9c27b0, #673ab7);
            border: 1rpx solid rgba(156, 39, 176, 0.5);
            border-radius: 15rpx;
            padding: 6rpx 12rpx;
            
            .trait-text {
              color: rgba(255, 255, 255, 0.9);
              font-size: 22rpx;
            }
          }
        }

        // 浮动特效
        .floating-avatar {
          animation: gentleFloat 4s ease-in-out infinite;

          image {
            animation: gentleBreathe 3s ease-in-out infinite;
            transform-origin: center center;
          }
        }

        .floating-avatar-delayed {
          animation: gentleFloat 4s ease-in-out infinite 2s;

          image {
            animation: gentleBreathe 3s ease-in-out infinite 1.5s;
            transform-origin: center center;
          }
        }
      }
      
      .close-btn {
        position: absolute;
        top: 20rpx;
        right: 20rpx;
        width: 60rpx;
        height: 60rpx;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.1);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 40rpx;
      }
    }
    
    .detail-body {
      padding: 40rpx;
      max-height: 50vh;
      overflow-y: auto;
      
      // Partner完整信息展示
      .partner-complete-section {
        margin-bottom: 50rpx;

        .partner-showcase-container {
          display: flex;
          flex-direction: column;
          align-items: center;
          padding: 40rpx;
          background: linear-gradient(135deg, rgba(255, 215, 0, 0.15), rgba(255, 215, 0, 0.05));
          border-radius: 30rpx;
          backdrop-filter: blur(15rpx);
          border: 2rpx solid rgba(255, 215, 0, 0.4);
          box-shadow: 0 0 30rpx rgba(255, 215, 0, 0.2);

          .partner-name {
            color: #FFD700;
            font-size: 40rpx;
            font-weight: bold;
            margin-bottom: 30rpx;
            display: block;
            text-align: center;
            text-shadow: 0 0 15rpx rgba(255, 215, 0, 0.8);
          }

          .partner-avatar-container {
            margin-bottom: 30rpx;

            .character-avatar {
              position: relative;
            }
          }

          .traits-display-enhanced {
            display: flex;
            flex-wrap: wrap;
            gap: 15rpx;
            justify-content: center;
            margin-bottom: 30rpx;

            .trait-tag-highlight {
              background: linear-gradient(45deg, rgba(255, 215, 0, 0.5), rgba(255, 215, 0, 0.7));
              border: 2rpx solid rgba(255, 215, 0, 0.9);
              border-radius: 10rpx;
              padding: 10rpx 20rpx;
              box-shadow: 0 0 20rpx rgba(255, 215, 0, 0.4);
              animation: trait-glow 2s ease-in-out infinite alternate;

              .trait-text {
                color: white;
                font-size: 26rpx;
                font-weight: bold;
                text-shadow: 0 0 8rpx rgba(0, 0, 0, 0.7);
              }
            }
          }

          .description-container {
            width: 100%;

            .desc-title {
              color: white;
              font-size: 32rpx;
              font-weight: bold;
              margin-bottom: 20rpx;
              display: block;
              text-align: center;
              text-shadow: 0 0 10rpx rgba(255, 255, 255, 0.5);
            }
          }
        }
      }

      .description-enhanced {
        margin-bottom: 40rpx;

        .desc-title {
          color: white;
          font-size: 32rpx;
          font-weight: bold;
          margin-bottom: 20rpx;
          display: block;
          text-align: center;
        }

        .swiper-container {
          background: rgba(255, 255, 255, 0.05);
          border-radius: 20rpx;
          padding: 20rpx;
          backdrop-filter: blur(10rpx);
          border: 1rpx solid rgba(255, 255, 255, 0.1);

          .description-swiper {
            height: 120rpx;

            .desc-item {
              display: flex;
              align-items: center;
              justify-content: center;
              height: 100%;
              padding: 0 20rpx;

              .desc-text {
                color: rgba(255, 255, 255, 0.9);
                font-size: 28rpx;
                line-height: 1.6;
                text-align: center;
                text-shadow: 0 0 5rpx rgba(0, 0, 0, 0.3);
              }
            }
          }
        }
      }
      
      .attributes {
        .attr-title {
          color: white;
          font-size: 32rpx;
          font-weight: bold;
          margin-bottom: 30rpx;
          display: block;
        }
        
        .character-showcase {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 40rpx;
          padding: 30rpx;
          background: rgba(255, 255, 255, 0.05);
          border-radius: 20rpx;
          backdrop-filter: blur(10rpx);
          
          .role-section, .partner-section {
            display: flex;
            flex-direction: column;
            align-items: center;
            flex: 1;
            
            .section-title {
              font-size: 28rpx;
              color: rgba(255, 255, 255, 0.8);
              margin-bottom: 20rpx;
              font-weight: 600;
            }
            
            .character-avatar {
              width: 120rpx;
              height: 120rpx;
              margin-bottom: 20rpx;
              
              image {
                width: 100%;
                height: 100%;
                border-radius: 50%;
                border: 3rpx solid rgba(255, 255, 255, 0.3);
              }
            }
            
            .traits-display {
              display: flex;
              flex-wrap: wrap;
              gap: 8rpx;
              justify-content: center;
              
              .trait-tag {
                background: rgba(156, 39, 176, 0.3);
                border: 1rpx solid rgba(156, 39, 176, 0.5);
                border-radius: 12rpx;
                padding: 4rpx 8rpx;
                
                &.partner-trait {
                  background: rgba(255, 215, 0, 0.3);
                  border: 1rpx solid rgba(255, 215, 0, 0.5);
                }
                
                .trait-text {
                  color: rgba(255, 255, 255, 0.9);
                  font-size: 20rpx;
                }
              }
            }
          }
          
          .vs-divider {
            font-size: 32rpx;
            font-weight: bold;
            color: rgba(255, 255, 255, 0.6);
            margin: 0 30rpx;
            animation: pulse 2s ease-in-out infinite;
          }
        }
        
        .radar-container-fixed {
          position: relative;
          width: 300rpx;
          height: 300rpx;
          margin: 0 auto 100rpx;

          .radar-chart-fixed {
            width: 100%;
            height: 100%;
          }

          .radar-labels-fixed {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;

            .radar-label-fixed {
              position: absolute;
              transform: translate(-50%, -50%);

              .label-text-fixed {
                font-size: 24rpx;
                font-weight: 600;
                color: rgba(255, 255, 255, 0.9);
                text-shadow: 0 0 8rpx rgba(0, 0, 0, 0.8);
                white-space: nowrap;
                background: rgba(0, 0, 0, 0.5);
                padding: 6rpx 12rpx;
                border-radius: 10rpx;
                backdrop-filter: blur(5rpx);
                border: 1rpx solid rgba(255, 255, 255, 0.2);
              }
            }
          }

          .radar-legend-fixed {
            position: absolute;
            bottom: -80rpx;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 20rpx;
            background: rgba(26, 26, 46, 0.9);
            padding: 15rpx 30rpx;
            border-radius: 25rpx;
            backdrop-filter: blur(15rpx);
            border: 1rpx solid rgba(255, 255, 255, 0.2);

            .legend-item {
              display: flex;
              align-items: center;
              gap: 10rpx;

              .legend-color {
                width: 24rpx;
                height: 24rpx;
                border-radius: 6rpx;
              }

              .legend-label {
                font-size: 26rpx;
                color: white;
                font-weight: 600;
                text-shadow: 0 0 4rpx rgba(0, 0, 0, 0.5);
              }
            }
          }
        }
        
        .attr-list {
          .attr-item {
            display: flex;
            align-items: center;
            margin-bottom: 25rpx;
            
            .attr-name {
              color: rgba(255, 255, 255, 0.9);
              font-size: 26rpx;
              width: 120rpx;
            }
            
            .attr-bar {
              flex: 1;
              height: 16rpx;
              background: rgba(255, 255, 255, 0.2);
              border-radius: 8rpx;
              margin: 0 20rpx;
              overflow: hidden;
              
              .attr-fill {
                height: 100%;
                background: linear-gradient(90deg, #4facfe, #00f2fe);
                border-radius: 8rpx;
                transition: width 0.3s ease;
              }
            }
            
            .attr-value {
              color: white;
              font-size: 26rpx;
              font-weight: bold;
              width: 60rpx;
              text-align: right;
            }
          }
        }
      }
    }
    
    .detail-footer {
      display: flex;
      padding: 30rpx 40rpx;
      gap: 20rpx;
      border-top: 1rpx solid rgba(255, 255, 255, 0.1);
      
      .action-btn {
        flex: 1;
        padding: 25rpx;
        border-radius: 15rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        
        text {
          font-size: 28rpx;
          font-weight: 500;
        }
        
        &.primary {
          background: linear-gradient(45deg, #667eea, #764ba2);
          
          text {
            color: white;
          }
        }
        
        &.secondary {
          background: rgba(255, 255, 255, 0.1);
          border: 1rpx solid rgba(255, 255, 255, 0.3);
          
          text {
            color: rgba(255, 255, 255, 0.8);
          }
        }
        
        &:active {
          transform: scale(0.98);
        }
      }
    }
  }
}

// 动画
@keyframes twinkle {
  0%, 100% { opacity: 0.3; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.2); }
}

@keyframes meteor-fall {
  0% {
    top: -10%;
    left: 110%;
  }
  100% {
    top: 110%;
    left: -10%;
  }
}

@keyframes rotate {
  from { transform: translate(-50%, -50%) rotate(0deg); }
  to { transform: translate(-50%, -50%) rotate(360deg); }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20rpx); }
}

@keyframes pulse {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 1; }
}

// 浮动特效动画
@keyframes gentleFloat {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-15rpx);
  }
}

@keyframes gentleBreathe {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

// 特质高亮动画
@keyframes trait-glow {
  0% {
    box-shadow: 0 0 15rpx rgba(255, 215, 0, 0.3);
    transform: scale(1);
  }
  100% {
    box-shadow: 0 0 25rpx rgba(255, 215, 0, 0.6);
    transform: scale(1.02);
  }
}

// 头像发光动画
@keyframes avatar-glow {
  0%, 100% {
    box-shadow: 0 0 20rpx rgba(255, 215, 0, 0.4);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 0 40rpx rgba(255, 215, 0, 0.8);
    transform: scale(1.05);
  }
}

.radar-comparison-container {
  position: relative;
  width: 300rpx;
  height: 300rpx;
  margin: 40rpx auto;
  background: rgba(26, 26, 46, 0.8);
  border-radius: 20rpx;
  padding: 20rpx;
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.3);
}

.radar-comparison-chart {
  width: 100%;
  height: 100%;
}

    .radar-comparison-labels {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;

      .radar-comparison-label {
        position: absolute;
        transform: translate(-50%, -50%);

        .label-text {
          font-size: 24rpx;
          color: rgba(255, 255, 255, 0.9);
          font-weight: 600;
          white-space: nowrap;
          background: rgba(0, 0, 0, 0.6);
          padding: 4rpx 8rpx;
          border-radius: 8rpx;
          backdrop-filter: blur(5rpx);
          border: 1rpx solid rgba(255, 255, 255, 0.2);
          text-shadow: 0 0 4rpx rgba(0, 0, 0, 0.8);
        }
      }
    }

    .radar-comparison-legend {
      position: absolute;
      bottom: -70rpx;
      left: 50%;
      transform: translateX(-50%);
      display: flex;
      justify-content: center;
      gap: 30rpx;
      background: linear-gradient(135deg, rgba(26, 26, 46, 0.95), rgba(40, 40, 70, 0.95));
      padding: 15rpx 25rpx;
      border-radius: 30rpx;
      backdrop-filter: blur(15rpx);
      border: 1rpx solid rgba(255, 255, 255, 0.3);
      box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.4);
      min-width: 200rpx;

      .legend-item {
        display: flex;
        align-items: center;
        gap: 8rpx;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-2rpx);
        }

        .legend-color {
          width: 20rpx;
          height: 20rpx;
          border-radius: 4rpx;
          box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);

          &[style*="FFD700"] {
            box-shadow: 0 2rpx 8rpx rgba(255, 215, 0, 0.4);
          }

          &[style*="2196f3"] {
            box-shadow: 0 2rpx 8rpx rgba(33, 150, 243, 0.4);
          }
        }

        .legend-label {
          font-size: 24rpx;
          color: rgba(255, 255, 255, 0.9);
          font-weight: 600;
          text-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.6);
        }
      }
    }

/* 雷达图动画效果 */
.radar-path-animate {
  animation: radarFadeIn 1.5s ease-out;
  transform-origin: center;
}

.radar-glow-gold {
  filter: drop-shadow(0 0 8rpx rgba(255, 215, 0, 0.6));
  animation: radarPulseGold 3s ease-in-out infinite;
}

.radar-glow-blue {
  filter: drop-shadow(0 0 8rpx rgba(33, 150, 243, 0.6));
  animation: radarPulseBlue 3s ease-in-out infinite;
}

@keyframes radarFadeIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes radarPulseGold {
  0%, 100% {
    filter: drop-shadow(0 0 8rpx rgba(255, 215, 0, 0.6));
  }
  50% {
    filter: drop-shadow(0 0 16rpx rgba(255, 215, 0, 0.9));
  }
}

@keyframes radarPulseBlue {
  0%, 100% {
    filter: drop-shadow(0 0 8rpx rgba(33, 150, 243, 0.6));
  }
  50% {
    filter: drop-shadow(0 0 16rpx rgba(33, 150, 243, 0.9));
  }
}
</style>