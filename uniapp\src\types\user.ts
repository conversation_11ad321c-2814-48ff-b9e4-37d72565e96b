/** 通用的用户信息 */
type BaseProfile = {
  /** 用户ID */
  id: number
  /** 头像  */
  avatar: string
  /** 昵称 */
  nickname?: string
}

/** 属性值类型 */
export type AttributeValues = {
  spontaneous: number
  collaborative: number
  realist: number
  logical: number
  analytical: number
  introvert: number
}

/** 公众号登录 登录用户信息 */
export type LoginResult = BaseProfile & {
  /** 手机号 */
  phone?: string
  /** 登录凭证 */
  token: string
  /** 算力 */
  tokens: number
  /** 创建时间 */
  createTime?: string
  /** 角色信息 */
  role?: {
    traits?: any
    description?: string
    attribute?: AttributeValues
  }
  /** 伙伴信息 */
  partner?: {
    nickName: string
    character: string
    traits?: any
    description?: string
    createTime?: string
    attribute?: AttributeValues
  }
}

/** 个人信息 用户详情信息 */
export type ProfileDetail = BaseProfile & {
  /** 性别 */
  gender?: Gender
  /** 生日 */
  birthday?: string
  /** 省市区 */
  fullLocation?: string
  /** 职业 */
  profession?: string
}
/** 性别 */
export type Gender = '女' | '男'

/** 个人信息 修改请求体参数 */
export type ProfileParams = Pick<
  ProfileDetail,
  'nickname' | 'gender' | 'birthday' | 'profession'
> & {
  /** 省份编码 */
  provinceCode?: string
  /** 城市编码 */
  cityCode?: string
  /** 区/县编码 */
  countyCode?: string
}
