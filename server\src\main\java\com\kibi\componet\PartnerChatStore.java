package com.kibi.componet;

import com.kibi.entity.CreateChat;
import com.kibi.entity.PartnerChat;
import com.kibi.service.CreateChatService;
import com.kibi.service.PartnerChatService;
import dev.langchain4j.data.message.ChatMessage;
import dev.langchain4j.data.message.ChatMessageDeserializer;
import dev.langchain4j.data.message.ChatMessageSerializer;
import dev.langchain4j.data.message.ChatMessageType;
import dev.langchain4j.store.memory.chat.ChatMemoryStore;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Component
public class PartnerChatStore implements ChatMemoryStore {

    @Autowired
    private PartnerChatService partnerChatService;

    @Override
    public List<ChatMessage> getMessages(Object memoryId) {
        PartnerChat partnerChat = partnerChatService.getById(memoryId.toString());
        if (partnerChat == null) return new ArrayList<>();
        List<ChatMessage> list = ChatMessageDeserializer.messagesFromJson(partnerChat.getMsg());
        
        // Ensure system messages are at the beginning
        List<ChatMessage> systemMessages = new ArrayList<>();
        List<ChatMessage> otherMessages = new ArrayList<>();
        
        for (ChatMessage message : list) {
            if (message.type() == ChatMessageType.SYSTEM) {
                systemMessages.add(message);
            } else {
                otherMessages.add(message);
            }
        }
        
        // Combine with system messages first
        List<ChatMessage> orderedMessages = new ArrayList<>();
        orderedMessages.addAll(systemMessages);
        orderedMessages.addAll(otherMessages);
        
        return orderedMessages;
    }

    @Transactional
    @Override
    public void updateMessages(Object memoryId, List<ChatMessage> list) {
        PartnerChat byId = partnerChatService.getById(memoryId.toString());
        String messagesJson = ChatMessageSerializer.messagesToJson(list);
        if (byId != null) {
            byId.setMsg(messagesJson);
            partnerChatService.updateById(byId);
        }
    }

    @Transactional
    @Override
    public void deleteMessages(Object memoryId) {
        // Implementation needed if you want to delete messages
    }
}