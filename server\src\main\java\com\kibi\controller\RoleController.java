package com.kibi.controller;

import com.alibaba.fastjson2.JSONArray;
import com.kibi.entity.Partner;
import com.kibi.entity.Role;
import com.kibi.entity.User;
import com.kibi.service.PartnerService;
import com.kibi.service.RoleService;
import com.kibi.service.UserService;
import com.kibi.utils.JWTUtils;
import com.kibi.utils.R;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/role")
public class RoleController {

    @Autowired
    private RoleService roleService;
    
    @Autowired
    private UserService userService;

    @Autowired
    private JWTUtils jwtUtils;

    /**
     * 获取角色信息
     */
    @GetMapping("/info")
    public R getPartnerInfo(@RequestHeader("Authorization") String authHeader) {
        try {
            // 验证token
            String token = jwtUtils.extractTokenFromHeader(authHeader);
            if (token == null || !jwtUtils.validateToken(token)) {
                return R.error("用户未登录或token无效");
            }

            // 获取当前用户ID
            Long currentUserId = jwtUtils.getUserIdFromToken(token);
            if (currentUserId == null) {
                return R.error("用户未登录");
            }

            // 查询伙伴信息
            Role role = roleService.getById(currentUserId);
            if (role == null) {
                return R.error("您还没有创建角色");
            }

            return R.success(role);

        } catch (Exception e) {
            e.printStackTrace();
            return R.error("系统异常：" + e.getMessage());
        }
    }
}