<script setup lang="ts">
import { onLaunch, onShow, onHide } from "@dcloudio/uni-app";
import { http } from '@/utils/http'
import { useUserStore, useTokenStore } from '@/stores'

const tokenStore = useTokenStore()
const userStore = useUserStore()

// 检查token是否有效
const validateToken = async () => {
  try {
    
    const res = await http<boolean>({
      method: 'GET',
      url: '/user/validateToken'
    })
    
    if (res.code == 200) {
      // 保存用户信息
      const userInfo = res.data
              userStore.setProfile({
                  id: userInfo.id,
                  avatar: userInfo.headImgUrl,
                  nickname: userInfo.nickName,
                  phone: userInfo.phone || '',
                  tokens: userInfo.tokens,
                  createTime: userInfo.createTime
              })

              const res3 = await http<any>({
                  url: '/role/info'
              })
              
              if (res3.code == 200) {
                if (userStore.profile) {
                  userStore.setProfile({
                    ...userStore.profile,
                    role: {
                      traits: res3.data.traits,
                      description: res3.data.description,
                      attribute: {
                        spontaneous: res3.data.spontaneous,
                        collaborative: res3.data.collaborative,
                        realist: res3.data.realist,
                        logical: res3.data.logical,
                        analytical: res3.data.analytical,
                        introvert: res3.data.introvert
                      }
                    }
                  })
                }
              }

              const res2 = await http<any>({
                  url: '/partner/info'
              })
              
              if (res2.code == 200) {
                if (userStore.profile) {
                  userStore.setProfile({
                    ...userStore.profile,
                    partner: {
                      nickName: res2.data.nickname,
                      style: res2.data.style,
                      traits: res2.data.traits,
                      description: res2.data.description,
                      createTime: res2.data.createTime,
                      attribute: {
                        spontaneous: res2.data.spontaneous,
                        collaborative: res2.data.collaborative,
                        realist: res2.data.realist,
                        logical: res2.data.logical,
                        analytical: res2.data.analytical,
                        introvert: res2.data.introvert
                      }
                    }
                  })
                }
              }
    } else {
      tokenStore.clearToken()
    }
  } catch (error) {
    tokenStore.clearToken()
  }
}

onLaunch(() => {

  // 应用启动时验证token
  if (tokenStore.token) {
    validateToken()
  }
});

onShow(() => {
});

onHide(() => {
});
</script>

<style lang="scss">
page {
  height: 100%;
}

//让uni.showToast提示信息在最上层显示
uni-toast{
  z-index: 19999 !important;
}

//让uni.showModal提示框在最上层显示
uni-modal{
  z-index:19999 !important;
}
</style>
