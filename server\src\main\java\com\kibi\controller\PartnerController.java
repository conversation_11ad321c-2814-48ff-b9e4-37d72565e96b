package com.kibi.controller;

import com.alibaba.fastjson2.JSONArray;
import com.kibi.entity.Partner;
import com.kibi.entity.User;
import com.kibi.service.PartnerService;
import com.kibi.service.UserService;
import com.kibi.utils.JWTUtils;
import com.kibi.utils.R;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/partner")
public class PartnerController {

    @Autowired
    private PartnerService partnerService;
    
    @Autowired
    private UserService userService;

    @Autowired
    private JWTUtils jwtUtils;

    /**
     * 获取伙伴信息
     */
    @GetMapping("/info")
    public R getPartnerInfo(@RequestHeader("Authorization") String authHeader) {
        try {
            // 验证token
            String token = jwtUtils.extractTokenFromHeader(authHeader);
            if (token == null || !jwtUtils.validateToken(token)) {
                return R.error("用户未登录或token无效");
            }

            // 获取当前用户ID
            Long currentUserId = jwtUtils.getUserIdFromToken(token);
            if (currentUserId == null) {
                return R.error("用户未登录");
            }

            // 查询伙伴信息
            Partner partner = partnerService.getById(currentUserId);
            if (partner == null) {
                return R.error("您还没有创建星灵");
            }

            // 调试信息
            System.out.println("Partner traits: " + partner.getTraits());
            System.out.println("Partner traits type: " + (partner.getTraits() != null ? partner.getTraits().getClass().getName() : "null"));

            return R.success(partner);

        } catch (Exception e) {
            e.printStackTrace();
            return R.error("系统异常：" + e.getMessage());
        }
    }

    /**
     * 更新伙伴昵称
     */
    @PostMapping("/updateNickname")
    public R updateNickname(String nickname, @RequestHeader("Authorization") String authHeader) {
        System.out.println(nickname);
        try {
            // 验证token
            String token = jwtUtils.extractTokenFromHeader(authHeader);
            if (token == null || !jwtUtils.validateToken(token)) {
                return R.error("用户未登录或token无效");
            }

            // 从token中获取用户ID
            Long userId = jwtUtils.getUserIdFromToken(token);
            if (userId == null) {
                return R.error("用户未登录");
            }

            if (nickname == null || nickname.trim().isEmpty()) {
                return R.error("昵称不能为空");
            }

            if (nickname.trim().length() > 20) {
                return R.error("昵称长度不能超过20个字符");
            }

            User user = userService.getById(userId);
            if (user == null) {
                return R.error("用户不存在");
            }

            Partner partner = partnerService.getById(userId);
            if (partner == null) return R.error("星灵不存在");
            partner.setNickname(nickname.trim());
            boolean b = partnerService.updateById(partner);

            if (b) {
                Map<String, Object> data = new HashMap<>();
                data.put("nickname", partner.getNickname());
                return R.success(data);
            } else {
                return R.error("更新失败，请稍后重试");
            }
        } catch (Exception e) {
            System.err.println("更新昵称异常: " + e.getMessage());
            e.printStackTrace();
            return R.error("更新失败");
        }
    }
}