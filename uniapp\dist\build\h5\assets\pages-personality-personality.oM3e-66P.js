import{d as a,af as l,A as e,q as s,c as t,w as n,i as c,o as i,a as o,O as d,j as r,f as u,F as m,r as _,p as h,b as f,e as p,t as b,u as y,B as g,n as v,l as z,m as k,a4 as x,as as w,at as M,au as C}from"./index-C6kXbWaK.js";import{_ as F}from"./back.DIErNce1.js";import{_ as P}from"./tasks.DKrLvW4J.js";import{_ as I}from"./_plugin-vue_export-helper.BCo6x5W8.js";const $="/static/ai/question.png",L="/static/ai/cancel2.png",D=I(a({__name:"personality",setup(a){const I=l(),D=e(10),R=e("你的"),S=e(!1),T=e(!1),B=e(!1),j=e(!1),W=s(()=>{var a,l;if(!(null==(l=null==(a=I.profile)?void 0:a.role)?void 0:l.attribute))return[{label:"Spontaneous",label_zh:"随性",value:0},{label:"Collaborative",label_zh:"协作",value:0},{label:"Realist",label_zh:"务实",value:0},{label:"Logical",label_zh:"逻辑",value:0},{label:"Analytical",label_zh:"分析",value:0},{label:"Introvert",label_zh:"内向",value:0}];const e=I.profile.role.attribute;return[{label:"Spontaneous",label_zh:"随性",value:e.spontaneous||0},{label:"Collaborative",label_zh:"协作",value:e.collaborative||0},{label:"Realist",label_zh:"务实",value:e.realist||0},{label:"Logical",label_zh:"逻辑",value:e.logical||0},{label:"Analytical",label_zh:"分析",value:e.analytical||0},{label:"Introvert",label_zh:"内向",value:e.introvert||0}]}),E=s(()=>{var a,l;if(!(null==(l=null==(a=I.profile)?void 0:a.partner)?void 0:l.attribute))return W.value.map(a=>({label:a.label,label_zh:a.label_zh,skychart:0,you:a.value}));const e=I.profile.partner.attribute;return W.value.map(a=>({label:a.label,label_zh:a.label_zh,skychart:e[a.label.toLowerCase()]||0,you:a.value}))}),A=[[{name:"Taste",name_zh:"品味",icon:"🍷"},{name:"Truth",name_zh:"真理",icon:"💎"},{name:"Power",name_zh:"力量",icon:"⚡"}],[{name:"Pages",name_zh:"页面",icon:"📖"},{name:"Worth",name_zh:"价值",icon:"💰"},{name:"Feel",name_zh:"感受",icon:"💝"}],[{name:"Impact",name_zh:"影响",icon:"🎯"},{name:"Play",name_zh:"游戏",icon:"🎮"},{name:"Create",name_zh:"创造",icon:"🎨"}],[{name:"Season",name_zh:"季节",icon:"🌸"},{name:"Care",name_zh:"关怀",icon:"💖"},{name:"Style",name_zh:"风格",icon:"👗"}],[{name:"Discover",name_zh:"发现",icon:"🔍"},{name:"Balance",name_zh:"平衡",icon:"⚖️"},{name:"Peace",name_zh:"和平",icon:"☮️"}],[{name:"Voice",name_zh:"声音",icon:"🎤"},{name:"Change",name_zh:"改变",icon:"🔄"},{name:"Bond",name_zh:"联结",icon:"🤝"}],[{name:"Dream",name_zh:"梦想",icon:"🌙"},{name:"Order",name_zh:"秩序",icon:"📋"},{name:"Home",name_zh:"家园",icon:"🏠"}],[{name:"Learn",name_zh:"学习",icon:"📚"},{name:"Romance",name_zh:"浪漫",icon:"💕"},{name:"Wander",name_zh:"漫游",icon:"🌍"}],[{name:"Watch",name_zh:"观察",icon:"👁️"},{name:"Connect",name_zh:"连接",icon:"🔗"},{name:"Reflect",name_zh:"反思",icon:"🪞"}],[{name:"Lead",name_zh:"领导",icon:"👑"},{name:"Music",name_zh:"音乐",icon:"🎵"},{name:"Grow",name_zh:"成长",icon:"🌱"}]],G=[{name:"Spontaneous",name_zh:"随性",description:"倾向于灵活应变，享受自由和即兴的生活方式，不喜欢过度规划",topics:[{name:"Wander",name_zh:"漫游",icon:"🌍"},{name:"Play",name_zh:"游戏",icon:"🎮"},{name:"Music",name_zh:"音乐",icon:"🎵"},{name:"Feel",name_zh:"感受",icon:"💝"},{name:"Romance",name_zh:"浪漫",icon:"💕"}]},{name:"Collaborative",name_zh:"协作",description:"重视团队合作，善于与他人建立联系，喜欢共同完成目标",topics:[{name:"Bond",name_zh:"联结",icon:"🤝"},{name:"Connect",name_zh:"连接",icon:"🔗"},{name:"Care",name_zh:"关怀",icon:"💖"},{name:"Voice",name_zh:"声音",icon:"🎤"},{name:"Lead",name_zh:"领导",icon:"👑"}]},{name:"Realist",name_zh:"务实",description:"注重实际效果和可行性，偏好具体可见的成果和实用的解决方案",topics:[{name:"Worth",name_zh:"价值",icon:"💰"},{name:"Order",name_zh:"秩序",icon:"📋"},{name:"Home",name_zh:"家园",icon:"🏠"},{name:"Power",name_zh:"力量",icon:"⚡"},{name:"Impact",name_zh:"影响",icon:"🎯"}]},{name:"Logical",name_zh:"逻辑",description:"依靠理性思维和逻辑推理来解决问题，重视客观分析和系统性思考",topics:[{name:"Truth",name_zh:"真理",icon:"💎"},{name:"Learn",name_zh:"学习",icon:"📚"},{name:"Pages",name_zh:"页面",icon:"📖"},{name:"Balance",name_zh:"平衡",icon:"⚖️"},{name:"Change",name_zh:"改变",icon:"🔄"}]},{name:"Analytical",name_zh:"分析",description:"善于深入分析和观察细节，喜欢探索事物的本质和规律",topics:[{name:"Watch",name_zh:"观察",icon:"👁️"},{name:"Discover",name_zh:"发现",icon:"🔍"},{name:"Reflect",name_zh:"反思",icon:"🪞"},{name:"Create",name_zh:"创造",icon:"🎨"},{name:"Grow",name_zh:"成长",icon:"🌱"}]},{name:"Introvert",name_zh:"内向",description:"偏好独处和深度思考，从内在世界获得能量，注重个人空间和内心体验",topics:[{name:"Dream",name_zh:"梦想",icon:"🌙"},{name:"Peace",name_zh:"和平",icon:"☮️"},{name:"Taste",name_zh:"品味",icon:"🍷"},{name:"Style",name_zh:"风格",icon:"👗"},{name:"Season",name_zh:"季节",icon:"🌸"}]}],O=()=>{const a=[];for(let l=1;l<=3;l++){const e=100*l/3;let s="";for(let a=0;a<6;a++){const l=(60*a-90)*Math.PI/180,t=150+e*Math.cos(l),n=150+e*Math.sin(l);0===a?s=`M ${t} ${n}`:s+=` L ${t} ${n}`}s+=" Z",a.push(s)}return a},Z=()=>{let a="";return E.value.forEach((l,e)=>{const s=(60*e-90)*Math.PI/180,t=l.skychart/32*100,n=150+t*Math.cos(s),c=150+t*Math.sin(s);0===e?a=`M ${n} ${c}`:a+=` L ${n} ${c}`}),a+=" Z",a},q=()=>{let a="";return E.value.forEach((l,e)=>{const s=(60*e-90)*Math.PI/180,t=l.you/32*100,n=150+t*Math.cos(s),c=150+t*Math.sin(s);0===e?a=`M ${n} ${c}`:a+=` L ${n} ${c}`}),a+=" Z",a},H=()=>{console.log("Starting next dimension...")},V=()=>{1==w().length?M({url:"/pages/ai/ai"}):C({delta:1})},J=s(()=>(()=>{let a=0;E.value.forEach(l=>{const e=Math.abs(l.skychart-l.you);a+=e});const l=a/E.value.length,e=Math.round(100-l/32*100);return Math.max(e,0)})()),N=s(()=>(()=>{let a=0,l="";return E.value.forEach(e=>{e.skychart>a&&(a=e.skychart,l=e.label_zh)}),l})()),K=()=>{S.value=!S.value},Q=()=>{T.value=!0},U=()=>{B.value=!0},X=()=>{j.value=!0},Y=()=>{T.value=!1,B.value=!1,j.value=!1};return(a,l)=>{const e=r,s=c,w=f,M=k,C=z;return i(),t(s,{class:"page-wrapper"},{default:n(()=>[o(s,{class:"personality-container"},{default:n(()=>[o(s,{class:"header-nav"},{default:n(()=>[o(s,{class:"back-btn",onClick:V},{default:n(()=>[o(e,{style:{width:"20px",height:"20px"},src:F,mode:"scaleToFill"})]),_:1}),o(s,{class:"nav-tabs"},{default:n(()=>[(i(),u(m,null,_(["你的","星灵","时空"],a=>o(s,{key:a,class:h(["nav-tab",{active:R.value===a}]),onClick:l=>(a=>{R.value=a})(a)},{default:n(()=>[o(w,{class:"nav-text"},{default:n(()=>[p(b(a),1)]),_:2},1024)]),_:2},1032,["class","onClick"])),64))]),_:1})]),_:1}),"你的"===R.value?(i(),t(s,{key:0,class:"main-content"},{default:n(()=>[o(s,{class:"character-card"},{default:n(()=>[o(e,{class:"bg-image",src:"https://img1.baidu.com/it/u=3548239810,287346742&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=667",mode:"aspectFill"}),o(s,{class:"character-overlay"},{default:n(()=>[o(w,{class:"character-name"},{default:n(()=>{var a;return[p(b(null==(a=y(I).profile)?void 0:a.nickname),1)]}),_:1}),o(s,{class:"traits-container"},{default:n(()=>[(i(!0),u(m,null,_(y(I).profile.role.traits,a=>(i(),t(s,{key:a,class:"trait-tag"},{default:n(()=>[o(w,{class:"trait-text"},{default:n(()=>[p(b(a),1)]),_:2},1024)]),_:2},1024))),128))]),_:1}),o(w,{class:"crystal-title"},{default:n(()=>[p("特性")]),_:1}),o(s,{class:h(["arrow-down",{expanded:S.value}]),onClick:K},{default:n(()=>[o(e,{style:{width:"100%",height:"100%"},src:P,mode:"scaleToFill"})]),_:1},8,["class"])]),_:1}),o(s,{class:h(["crystal-desc",{expanded:S.value}])},{default:n(()=>[p(b(y(I).profile.role.description),1)]),_:1},8,["class"])]),_:1}),o(s,{class:"radar-section"},{default:n(()=>[o(w,{class:"radar-title"},{default:n(()=>[p("天空视图 "),o(e,{style:{width:"30rpx",height:"30rpx","margin-left":"10rpx"},src:$,mode:"aspectFit",onClick:X})]),_:1}),o(s,{class:"radar-container"},{default:n(()=>[(i(),u("svg",{class:"radar-chart",viewBox:"0 0 300 300"},[g("g",{class:"radar-grid"},[(i(!0),u(m,null,_(O(),(a,l)=>(i(),u("path",{key:l,d:a,fill:"none",stroke:"rgba(255,255,255,0.2)","stroke-width":"1"},null,8,["d"]))),128))]),g("path",{d:q(),fill:"rgba(33, 150, 243, 0.3)",stroke:"#2196f3","stroke-width":"2"},null,8,["d"])])),o(s,{class:"radar-labels"},{default:n(()=>[(i(!0),u(m,null,_(E.value,(a,l)=>(i(),t(s,{key:l,class:"radar-label",style:v({left:150+120*Math.cos((60*l-90)*Math.PI/180)+"px",top:150+120*Math.sin((60*l-90)*Math.PI/180)+"px"})},{default:n(()=>[o(w,{class:"label-text"},{default:n(()=>[p(b(a.label_zh),1)]),_:2},1024)]),_:2},1032,["style"]))),128))]),_:1})]),_:1})]),_:1}),o(s,{class:"dimensions-section"},{default:n(()=>[o(s,{class:"dimensions-header"},{default:n(()=>[o(w,{class:"dimensions-title"},{default:n(()=>[p("维度")]),_:1}),o(s,{class:"progress-container"},{default:n(()=>[o(s,{class:"progress-bar"},{default:n(()=>[o(s,{class:"progress-fill",style:v({width:D.value+"%"})},null,8,["style"])]),_:1}),o(w,{class:"progress-text"},{default:n(()=>[p(b(D.value)+"%",1)]),_:1})]),_:1})]),_:1}),o(w,{class:"dimensions-desc"},{default:n(()=>[p(" 深入一个维度，反思它在你生活中的意义，然后获得个性化解读。 ")]),_:1}),o(s,{class:"start-btn",onClick:H},{default:n(()=>[o(w,{class:"start-text"},{default:n(()=>[p("选择维度交流")]),_:1})]),_:1})]),_:1}),o(s,{class:"dimensions-grid"},{default:n(()=>[(i(),u(m,null,_(A,(a,l)=>o(s,{key:l,class:"dimension-row"},{default:n(()=>[(i(!0),u(m,null,_(a,a=>(i(),t(s,{key:a.name,class:"dimension-item"},{default:n(()=>[o(s,{class:"dimension-icon"},{default:n(()=>[o(w,{class:"icon-emoji"},{default:n(()=>[p(b(a.icon),1)]),_:2},1024)]),_:2},1024),o(w,{class:"dimension-name"},{default:n(()=>[p(b(a.name_zh),1)]),_:2},1024)]),_:2},1024))),128))]),_:2},1024)),64))]),_:1})]),_:1})):d("",!0),"星灵"===R.value?(i(),t(s,{key:1,class:"companion-content"},{default:n(()=>[o(s,{class:"companion-main"},{default:n(()=>[o(w,{class:"page-title"},{default:n(()=>[p(b(y(I).profile.partner.nickName),1)]),_:1}),o(s,{class:"companion-traits"},{default:n(()=>[(i(!0),u(m,null,_(y(I).profile.partner.traits,a=>(i(),t(s,{key:a,class:"companion-trait-tag"},{default:n(()=>[o(w,{class:"companion-trait-text"},{default:n(()=>[p(b(a),1)]),_:2},1024)]),_:2},1024))),128))]),_:1}),o(s,null,{default:n(()=>[o(s,{class:"rabbit-container"},{default:n(()=>[o(e,{src:y(I).profile.partner.style,class:"animated-rabbit",style:{width:"300rpx"},mode:"widthFix"},null,8,["src"])]),_:1})]),_:1}),o(C,{class:"companion-description","indicator-dots":"",autoplay:"",circular:""},{default:n(()=>[(i(!0),u(m,null,_(y(I).profile.partner.description,a=>(i(),t(M,{key:a.id},{default:n(()=>[o(w,null,{default:n(()=>[p("......"+b(a),1)]),_:2},1024)]),_:2},1024))),128))]),_:1})]),_:1}),o(s,{class:"oracle-matched-section"},{default:n(()=>[o(s,{class:"companion-footer"},{default:n(()=>[o(s,{class:"compatibility-section"},{default:n(()=>[o(s,{class:"compatibility-label"},{default:n(()=>[p("匹配度 "),o(e,{style:{width:"30rpx",height:"30rpx","margin-left":"10rpx"},src:$,mode:"scaleToFill",onClick:Q})]),_:1}),o(s,{class:"compatibility-value"},{default:n(()=>[p(b(J.value)+"%",1)]),_:1}),o(s,{class:"compatibility-bar"},{default:n(()=>[o(s,{class:"compatibility-fill",style:v({width:J.value+"%"})},null,8,["style"])]),_:1})]),_:1}),o(s,{class:"core-dimension"},{default:n(()=>[o(s,{class:"core-label"},{default:n(()=>[p("核心维度 "),o(e,{style:{width:"30rpx",height:"30rpx","margin-left":"10rpx"},src:$,mode:"scaleToFill",onClick:U})]),_:1}),o(s,{class:"core-value"},{default:n(()=>[p(b(N.value),1)]),_:1})]),_:1})]),_:1}),o(w,{class:"oracle-matched-title"},{default:n(()=>[p("“星灵”匹配度")]),_:1}),o(s,{class:"radar-comparison-container"},{default:n(()=>[(i(),u("svg",{class:"radar-comparison-chart",viewBox:"0 0 300 300"},[g("defs",null,[g("mask",{id:"aiRadarMask"},[g("rect",{width:"300",height:"300",fill:"white"}),g("path",{d:q(),fill:"black"},null,8,["d"])])]),g("g",{class:"radar-grid"},[(i(!0),u(m,null,_(O(),(a,l)=>(i(),u("path",{key:l,d:a,fill:"none",stroke:"rgba(255,255,255,0.2)","stroke-width":"1"},null,8,["d"]))),128))]),g("path",{d:Z(),fill:"rgba(255, 215, 0, 0.3)",stroke:"none",mask:"url(#aiRadarMask)"},null,8,["d"]),g("path",{d:Z(),fill:"none",stroke:"#FFD700","stroke-width":"2"},null,8,["d"]),g("path",{d:q(),fill:"rgba(255, 255, 255, 0.3)",stroke:"#2196f3","stroke-width":"2"},null,8,["d"])])),o(s,{class:"radar-comparison-labels"},{default:n(()=>[(i(!0),u(m,null,_(E.value,(a,l)=>(i(),t(s,{key:l,class:"radar-comparison-label",style:v({left:150+120*Math.cos((60*l-90)*Math.PI/180)+"px",top:150+120*Math.sin((60*l-90)*Math.PI/180)+"px"})},{default:n(()=>[o(w,{class:"label-text"},{default:n(()=>[p(b(a.label_zh),1)]),_:2},1024)]),_:2},1032,["style"]))),128))]),_:1}),o(s,{class:"radar-comparison-legend"},{default:n(()=>[o(s,{class:"legend-item"},{default:n(()=>[o(s,{class:"legend-color",style:{"background-color":"#FFD700"}}),o(w,{class:"legend-label"},{default:n(()=>[p("星灵")]),_:1})]),_:1}),o(s,{class:"legend-item"},{default:n(()=>[o(s,{class:"legend-color",style:{"background-color":"#2196f3"}}),o(w,{class:"legend-label"},{default:n(()=>[p("你的")]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})):d("",!0),"时空"===R.value?(i(),t(s,{key:2,class:"city-content"},{default:n(()=>[o(s,{class:"city-main"},{default:n(()=>[o(s,{class:"city-header"},{default:n(()=>[o(w,{class:"city-subtitle"},{default:n(()=>[p("你的城市")]),_:1}),o(w,{class:"city-title"},{default:n(()=>[p("清远大学城")]),_:1}),o(w,{class:"city-type"},{default:n(()=>[p("(智慧之城)")]),_:1})]),_:1}),o(s,{class:"city-image-container"},{default:n(()=>[o(s,{class:"sky-background"},{default:n(()=>[(i(),u(m,null,_(15,a=>o(s,{key:a,class:"cloud",style:v({left:100*Math.random()+"%",top:40*Math.random()+"%",animationDelay:5*Math.random()+"s"})},null,8,["style"])),64)),o(s,{class:"flying-vehicle"})]),_:1}),o(s,{class:"city-skyline"},{default:n(()=>[o(s,{class:"city-buildings"},{default:n(()=>[o(s,{class:"building building-1"}),o(s,{class:"building building-2"}),o(s,{class:"building building-3"}),o(s,{class:"building building-4"}),o(s,{class:"building building-5"}),o(s,{class:"city-glow"})]),_:1})]),_:1})]),_:1}),o(s,{class:"city-description"},{default:n(()=>[o(w,{class:"description-text"},{default:n(()=>[p(" 你和星灵共同建设的专属城市，这里汇聚着知识与智慧，随着你们关系的发展而不断成长。城市的建筑风格与学术氛围体现着你们对学习和成长的共同追求。 ")]),_:1})]),_:1}),o(s,{class:"campus-core-section"},{default:n(()=>[o(s,{class:"campus-landmark-card"},{default:n(()=>[o(s,{class:"library-building"},{default:n(()=>[o(s,{class:"library-facade"},{default:n(()=>[o(s,{class:"main-structure"}),o(s,{class:"columns"},{default:n(()=>[(i(),u(m,null,_(4,a=>o(s,{key:a,class:"column"})),64))]),_:1}),o(s,{class:"steps"}),o(s,{class:"windows"},{default:n(()=>[(i(),u(m,null,_(6,a=>o(s,{key:a,class:"window",style:v({animationDelay:.3*a+"s"})},null,8,["style"])),64))]),_:1}),o(s,{class:"campus-trees"},{default:n(()=>[(i(),u(m,null,_(5,a=>o(s,{key:a,class:"tree",style:v({left:80*Math.random()+10+"%",animationDelay:2*Math.random()+"s"})},null,8,["style"])),64))]),_:1}),o(s,{class:"student-activities"},{default:n(()=>[(i(),u(m,null,_(3,a=>o(s,{key:a,class:"student",style:v({left:60*Math.random()+20+"%",animationDelay:4*Math.random()+"s"})},null,8,["style"])),64))]),_:1})]),_:1})]),_:1})]),_:1}),o(s,{class:"academic-stats-section"},{default:n(()=>[o(s,{class:"stats-display"},{default:n(()=>[o(s,{class:"stat-item"},{default:n(()=>[o(w,{class:"stat-value"},{default:n(()=>[p("12k")]),_:1}),o(w,{class:"stat-label"},{default:n(()=>[p("星灵")]),_:1})]),_:1}),o(s,{class:"stats-divider"})]),_:1}),o(s,{class:"activity-indicator"},{default:n(()=>[o(s,{class:"activity-ring"},{default:n(()=>[o(s,{class:"ring-progress",style:v({transform:"rotate(252deg)"})},null,8,["style"])]),_:1}),o(w,{class:"activity-text"},{default:n(()=>[p("活跃度 85%")]),_:1})]),_:1})]),_:1})]),_:1}),o(s,{class:"city-info-with-thumbnails"},{default:n(()=>[o(s,{class:"city-info-section"},{default:n(()=>[o(w,{class:"city-info-text"},{default:n(()=>[p(" 在清远大学城中，学术建筑与现代化设施和谐共存，营造出浓厚的学习氛围。这里有图书馆、实验楼、学生宿舍和文化中心，见证着你们在知识海洋中的每一次探索与成长。 ")]),_:1})]),_:1}),o(s,{class:"thumbnail-grid"},{default:n(()=>[o(s,{class:"thumbnail-row"},{default:n(()=>[o(s,{class:"thumbnail-item"},{default:n(()=>[o(s,{class:"thumbnail-image thumbnail-1",title:"图书馆区"},{default:n(()=>[o(s,{class:"thumb-landscape"},{default:n(()=>[o(s,{class:"thumb-library-sky"}),o(s,{class:"thumb-library-building"}),o(s,{class:"thumb-library-steps"}),o(s,{class:"thumb-library-trees"})]),_:1})]),_:1})]),_:1}),o(s,{class:"thumbnail-item"},{default:n(()=>[o(s,{class:"thumbnail-image thumbnail-2",title:"体育场区"},{default:n(()=>[o(s,{class:"thumb-landscape"},{default:n(()=>[o(s,{class:"thumb-sports-sky"}),o(s,{class:"thumb-sports-field"}),o(s,{class:"thumb-sports-track"}),o(s,{class:"thumb-sports-stands"})]),_:1})]),_:1})]),_:1})]),_:1}),o(s,{class:"thumbnail-row"},{default:n(()=>[o(s,{class:"thumbnail-item"},{default:n(()=>[o(s,{class:"thumbnail-image thumbnail-3",title:"实验楼区"},{default:n(()=>[o(s,{class:"thumb-landscape"},{default:n(()=>[o(s,{class:"thumb-lab-sky"}),o(s,{class:"thumb-lab-building"}),o(s,{class:"thumb-lab-windows"}),o(s,{class:"thumb-lab-ground"})]),_:1})]),_:1})]),_:1}),o(s,{class:"thumbnail-item"},{default:n(()=>[o(s,{class:"thumbnail-image thumbnail-4",title:"宿舍区"},{default:n(()=>[o(s,{class:"thumb-landscape"},{default:n(()=>[o(s,{class:"thumb-dorm-sky"}),o(s,{class:"thumb-dorm-buildings"}),o(s,{class:"thumb-dorm-courtyard"}),o(s,{class:"thumb-dorm-path"})]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})):d("",!0)]),_:1}),T.value?(i(),t(s,{key:0,class:"modal-overlay",onClick:Y},{default:n(()=>[o(s,{class:"modal-content",onClick:l[0]||(l[0]=x(()=>{},["stop"]))},{default:n(()=>[o(s,{class:"modal-header"},{default:n(()=>[o(w,{class:"modal-title"},{default:n(()=>[p("匹配度")]),_:1}),o(s,{class:"modal-close",onClick:Y},{default:n(()=>[o(s,{class:"close-icon"},{default:n(()=>[o(e,{style:{width:"50rpx",height:"50rpx"},src:L,mode:"aspectFit"})]),_:1})]),_:1})]),_:1}),o(s,{class:"modal-body"},{default:n(()=>[o(w,{class:"modal-text"},{default:n(()=>[p(" 每个星灵也都有自己的核心维度！了解你的核心维度与他们的核心维度如何契合，能够增进你们之间的联系。这种契合会揭示出你的沟通风格、情感需求以及关系模式，帮助你培养共情能力并建立有意义的联系 。 ")]),_:1})]),_:1})]),_:1})]),_:1})):d("",!0),B.value?(i(),t(s,{key:1,class:"modal-overlay",onClick:Y},{default:n(()=>[o(s,{class:"modal-content",onClick:l[1]||(l[1]=x(()=>{},["stop"]))},{default:n(()=>[o(s,{class:"modal-header"},{default:n(()=>[o(w,{class:"modal-title"},{default:n(()=>[p("核心维度")]),_:1}),o(s,{class:"modal-close",onClick:Y},{default:n(()=>[o(s,{class:"close-icon"},{default:n(()=>[o(e,{style:{width:"50rpx",height:"50rpx"},src:L,mode:"aspectFit"})]),_:1})]),_:1})]),_:1}),o(s,{class:"modal-body"},{default:n(()=>[o(w,{class:"modal-text"},{default:n(()=>[p(" 星灵的核心维度是其最突出的特质，是让每个星灵独一无二的内在闪光点 。 ")]),_:1})]),_:1})]),_:1})]),_:1})):d("",!0),j.value?(i(),t(s,{key:2,class:"modal-overlay",onClick:Y},{default:n(()=>[o(s,{class:"modal-content personality-modal",onClick:l[2]||(l[2]=x(()=>{},["stop"]))},{default:n(()=>[o(s,{class:"modal-header"},{default:n(()=>[o(w,{class:"modal-title"},{default:n(()=>[p("维度介绍")]),_:1}),o(s,{class:"modal-close",onClick:Y},{default:n(()=>[o(s,{class:"close-icon"},{default:n(()=>[o(e,{style:{width:"50rpx",height:"50rpx"},src:L,mode:"aspectFit"})]),_:1})]),_:1})]),_:1}),o(s,{class:"modal-body"},{default:n(()=>[o(s,{class:"all-personalities"},{default:n(()=>[(i(),u(m,null,_(G,a=>o(s,{key:a.name,class:h(["personality-type-item",{"core-dimension":a.name_zh===N.value}])},{default:n(()=>[o(s,{class:"type-header"},{default:n(()=>[o(w,{class:"type-name"},{default:n(()=>[p(b(a.name_zh),1)]),_:2},1024),a.name_zh===N.value?(i(),t(s,{key:0,class:"current-badge"},{default:n(()=>[o(w,{class:"badge-text"},{default:n(()=>[p("核心")]),_:1})]),_:1})):d("",!0)]),_:2},1024),o(s,{class:"type-traits"},{default:n(()=>[o(s,{class:"dimensions-row"},{default:n(()=>[o(w,{class:"dimensions-label"},{default:n(()=>[p("维度：")]),_:1}),o(s,{class:"dimensions-tags"},{default:n(()=>[(i(!0),u(m,null,_(a.topics,a=>(i(),t(s,{key:a.name,class:"dimension-tag"},{default:n(()=>[o(w,{class:"dimension-tag-text"},{default:n(()=>[p(b(a.icon)+" "+b(a.name_zh),1)]),_:2},1024)]),_:2},1024))),128))]),_:2},1024)]),_:2},1024)]),_:2},1024),o(s,{class:"type-description"},{default:n(()=>[o(w,{class:"type-description-text"},{default:n(()=>[p(b(a.description),1)]),_:2},1024)]),_:2},1024)]),_:2},1032,["class"])),64))]),_:1})]),_:1})]),_:1})]),_:1})):d("",!0)]),_:1})}}}),[["__scopeId","data-v-f124bd7c"]]);export{D as default};
