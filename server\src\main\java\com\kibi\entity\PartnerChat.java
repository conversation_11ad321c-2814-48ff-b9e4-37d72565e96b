package com.kibi.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@TableName("tb_partner_chat")
public class PartnerChat {

    @TableId(type = IdType.ASSIGN_ID)
    private Long id;
    private Integer userId;
    private String msg;
    private LocalDate diaryDate;

    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
}
