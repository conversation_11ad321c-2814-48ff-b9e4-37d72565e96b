<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useUserStore } from '@/stores'
import { http } from '@/utils/http'
import { mockRankingData } from '@/mock/rankingData'
import type { RankingUser, Dimension } from '@/types/ranking'

const userStore = useUserStore()

// 六个维度定义
const dimensions = ref<Dimension[]>([
  { name: '随性', icon: '🚀', key: 'spontaneous' },
  { name: '协作', icon: '🤝', key: 'collaborative' },
  { name: '现实', icon: '🎯', key: 'realist' },
  { name: '逻辑', icon: '🧠', key: 'logical' },
  { name: '分析', icon: '📊', key: 'analytical' },
  { name: '内向', icon: '🤔', key: 'introvert' }
])

const currentDimension = ref<number>(0)
const rankingList = ref<RankingUser[]>([])
const loading = ref<boolean>(false)
const myRank = ref<number>(0)
const myScore = ref<number>(0)

// 切换维度
const switchDimension = (index: number) => {
  currentDimension.value = index
  loadRankingData()
}

// 获取奖牌图标
const getMedalIcon = (index: number) => {
  const medals = ['🥇', '🥈', '🥉']
  return medals[index]
}

// 加载排行榜数据
const loadRankingData = async () => {
  loading.value = true
  try {
    const dimension = dimensions.value[currentDimension.value]
    
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 使用模拟数据
    const rankingData = mockRankingData[dimension.key]
    rankingList.value = rankingData.list || []
    myRank.value = rankingData.myRank || 0
    myScore.value = rankingData.myScore || 0
    
  } catch (error) {
    console.error('加载排行榜失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

// 返回上一页
const goBack = () => {
  if (getCurrentPages().length == 1) {
    uni.reLaunch({ url: '/pages/ai/ai' })
  } else {
    uni.navigateBack({ delta: 1})
  }
}

onMounted(() => {
  loadRankingData()
})
</script>

<template>
  <view class="ranking-container">
    <!-- Header -->
    <view class="header">
      <view class="header-left" @tap="goBack">
        <image src="/static/ai/back.png" class="back-icon" />
      </view>
      <view class="header-title">排行榜</view>
      <view class="header-right"></view>
    </view>

    <!-- Dimension Tabs -->
    <view class="dimension-tabs">
      <scroll-view scroll-x="true" class="tabs-scroll">
        <view class="tabs-container">
          <view 
            v-for="(dimension, index) in dimensions" 
            :key="index"
            class="tab-item"
            :class="{ active: currentDimension === index }"
            @tap="switchDimension(index)"
          >
            <text class="tab-icon">{{ dimension.icon }}</text>
            <text class="tab-text">{{ dimension.name }}</text>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- Current User Rank -->
    <view class="my-rank-card" v-if="userStore.profile">
      <view class="my-rank-content">
        <view class="rank-info">
          <text class="my-rank-text">我的排名</text>
          <text class="my-rank-number">🏆 {{ myRank }}</text>
        </view>
        <view class="user-info">
          <image :src="userStore.profile.avatar" class="user-avatar" />
          <view class="user-details">
            <text class="user-name">{{ userStore.profile.nickname }}</text>
            <text class="user-score">{{ myScore }}分</text>
          </view>
        </view>
      </view>
    </view>

    <!-- Ranking List -->
    <view class="ranking-list">
      <view 
        v-for="(user, index) in rankingList" 
        :key="user.id"
        class="ranking-item"
        :class="{ 'top-three': index < 3 }"
      >
        <!-- Rank Number -->
        <view class="rank-number">
          <view v-if="index < 3" class="medal">
            <text class="medal-icon">{{ getMedalIcon(index) }}</text>
          </view>
          <text v-else class="rank-text">{{ index + 1 }}</text>
        </view>

        <!-- User Info -->
        <view class="user-section">
          <image :src="user.avatar" class="avatar" />
          <view class="user-info">
            <text class="username">{{ user.nickname }}</text>
            <text class="user-level">Lv.{{ user.level }}</text>
          </view>
        </view>

        <!-- Trend -->
        <view class="trend-section">
          <image 
            :src="user.trend > 0 ? '/static/ai/up.png' : user.trend < 0 ? '/static/ai/down.png' : '/static/ai/equal.png'" 
            class="trend-icon" 
          />
          <text class="trend-text" :class="{ 
            'trend-up': user.trend > 0, 
            'trend-down': user.trend < 0 
          }">
            {{ user.trend > 0 ? '+' + user.trend : user.trend}}
          </text>
        </view>

        <!-- Score -->
        <view class="score-section">
          <text class="score">{{ user.score }}</text>
          <text class="score-unit"></text>
        </view>
      </view>
    </view>

    <!-- Loading -->
    <view v-if="loading" class="loading">
      <text>加载中...</text>
    </view>

    <!-- Empty State -->
    <view v-if="!loading && rankingList.length === 0" class="empty-state">
      <text class="empty-text">暂无排行数据</text>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.ranking-container {
  min-height: 100vh;
  background: rgba(26, 26, 46, 0.9);
  color: white;
  position: relative;
  overflow-x: hidden;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40rpx 30rpx 20rpx;
  position: sticky;
  top: 0;
  z-index: 100;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(15rpx);
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.1);
}

.header-left, .header-right {
  width: 80rpx;
}

.back-icon {
  width: 40rpx;
  height: 40rpx;
}

.header-title {
  font-size: 36rpx;
  font-weight: 600;
  text-align: center;
}

.dimension-tabs {
  padding: 20rpx 0;
  background: rgba(255, 255, 255, 0.05);
}

.tabs-scroll {
  white-space: nowrap;
}

.tabs-container {
  display: flex;
  padding: 0 30rpx;
  gap: 20rpx;
}

.tab-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 30rpx;
  border-radius: 20rpx;
  background: rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  min-width: 120rpx;
  
  &.active {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.05);
  }
}

.tab-icon {
  font-size: 32rpx;
  margin-bottom: 8rpx;
}

.tab-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
}

.my-rank-card {
  margin: 30rpx;
  padding: 30rpx;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(15rpx);
  border-radius: 20rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.my-rank-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.rank-info {
  display: flex;
  flex-direction: column;
}

.my-rank-text {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 8rpx;
}

.my-rank-number {
  font-size: 48rpx;
  font-weight: 700;
  color: #ffd700;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.user-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
}

.user-details {
  display: flex;
  flex-direction: column;
}

.user-name {
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.user-score {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

.ranking-list {
  padding: 0 30rpx 30rpx;
}

.ranking-item {
  display: flex;
  align-items: center;
  padding: 25rpx;
  margin-bottom: 20rpx;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10rpx);
  border-radius: 16rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  
  &.top-three {
    background: rgba(255, 215, 0, 0.15);
    border-color: rgba(255, 215, 0, 0.3);
  }
  
  &:active {
    transform: scale(0.98);
  }
}

.rank-number {
  width: 80rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.medal {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.medal-icon {
  font-size: 40rpx;
}

.rank-text {
  font-size: 32rpx;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
}

.user-section {
  flex: 1;
  display: flex;
  align-items: center;
  margin-left: 20rpx;
}

.avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.username {
  font-size: 30rpx;
  font-weight: 500;
  margin-bottom: 6rpx;
}

.user-level {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
}

.score-section {
  display: flex;
  align-items: baseline;
}

.score {
  font-size: 36rpx;
  font-weight: 600;
  color: #ffd700;
}

.score-unit {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
  margin-left: 4rpx;
}

.trend-section {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-right: 30rpx;
}

.trend-icon {
  width: 24rpx;
  height: 24rpx;
}

.trend-text {
  font-size: 24rpx;
  
  &.trend-up {
    color: #10b981;
  }
  
  &.trend-down {
    color: #ef4444;
  }
}

.loading, .empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 100rpx;
}

.empty-text {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.6);
}
</style>