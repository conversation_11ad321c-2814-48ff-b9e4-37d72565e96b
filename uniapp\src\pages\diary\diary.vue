<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'

// 响应式数据
const selectedDate = ref(new Date().toISOString().split('T')[0])
const showDateModal = ref(false)

// 示例日记数据
const diaryList = ref([
  {
    id: 1,
    image: '/static/diary2.png',
    content: '昨天就是这样的日子，从文殊中挤出话来就像从石头里挤水一样困难——但老实说？我还挺喜欢的。有一些东西让他们关于他们的最低限度的…',
    author: 'Skychart',
    date: '7月23日，星期三'
  },
  {
    id: 2,
    image: '/static/diary1.png',
    content: '今天厨房的温暖使我想起了家。各种成分结合在一起的方式有一种神奇的魔力，就像我们的谈话中思想和感情的融合一样……',
    author: 'Skychart',
    date: '7月22日，星期二'
  }
])

// 格式化日期显示
const formatDate = (dateStr: string) => {
  const date = new Date(dateStr)
  const options: Intl.DateTimeFormatOptions = { 
    weekday: 'long', 
    month: 'short', 
    day: 'numeric' 
  }
  return date.toLocaleDateString('en-US', options).toUpperCase()
}

// 显示日期选择器
const showDatePicker = () => {
  showDateModal.value = true
}

// 隐藏日期选择器
const hideDatePicker = () => {
  showDateModal.value = false
}

// 日期改变事件
const onDateChange = (e: any) => {
  selectedDate.value = e.detail.value
  hideDatePicker()
  // 这里可以根据选择的日期加载对应的日记数据
  loadDiaryByDate(selectedDate.value)
}

// 根据日期加载日记
const loadDiaryByDate = (date: string) => {
  console.log('Loading diary for date:', date)
  // 这里可以调用API加载对应日期的日记数据
}

// 返回上一页
const goBack = () => {
  if (getCurrentPages().length == 1) {
    uni.reLaunch({ url: '/pages/ai/ai' })
  } else {
    uni.navigateBack({ delta: 1})
  }
}

// 页面加载时获取今日日记
onMounted(() => {
  loadDiaryByDate(selectedDate.value)
})

const toInfo = () => {
  uni.navigateTo({ url: '/pages/diary/diaryInfo' })
}
</script>

<template>
  <view class="diary-container">
    <!-- 顶部导航栏 -->
    <view class="header-nav">
      <!-- 返回按钮 -->
      <view class="back-btn" @click="goBack">
        <image
          style="width: 30rpx; height: 30rpx;"
          src="/static/ai/back2.png"
          mode="aspectFit"
        />
      </view>
      
      <!-- 日期选择器 -->
      <view class="date-selector" @click="showDatePicker">
        <text class="date-text">7月24日，星期四</text>
      </view>
      
      <!-- 右侧图标 -->
      <view class="right-icon">
        🔓
      </view>
    </view>

    <!-- 内容区域 -->
    <view class="content-area">
      <!-- 日记卡片列表 -->
      <view class="diary-list">
        <view
        @click="toInfo"
          v-for="(diary, index) in diaryList" 
          :key="index"
          class="diary-card"
        >
          <!-- 日记图片 -->
          <view class="diary-image-container">
            <image 
              :src="diary.image" 
              class="diary-image"
              mode="aspectFill"
            />
          </view>
          
          <!-- 日记内容 -->
          <view class="diary-content">
            <text class="diary-text">{{ diary.content }}</text>
          </view>
          
          <!-- 底部信息 -->
          <view class="diary-footer">
            <text class="author-text">By {{ diary.author }}</text>
            <text class="date-text">{{ diary.date }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 日期选择器弹窗 -->
    <view v-if="showDateModal" class="date-modal-overlay" @click="hideDatePicker">
      <view class="date-modal" @click.stop>
        <picker 
          mode="date" 
          :value="selectedDate" 
          @change="onDateChange"
        >
          <view class="picker-content">
            <text>选择日期</text>
          </view>
        </picker>
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
.diary-container {
  width: 100%;
  min-height: 100vh;
  background: #f6f6f6;
}

.header-nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 60rpx 32rpx 30rpx;
  background: white;
  
  .back-btn {
    width: 48rpx;
    height: 48rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    
    &:hover {
      opacity: 0.7;
    }
  }
  
  .date-selector {
    flex: 1;
    display: flex;
    justify-content: center;
    cursor: pointer;
    
    .date-text {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
      letter-spacing: 2rpx;
    }
  }
  
  .right-icon {
    width: 48rpx;
    height: 48rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    
    &:hover {
      opacity: 0.7;
    }
  }
}

.content-area {
  padding: 20rpx 32rpx 40rpx;
}

.diary-list {
  display: flex;
  flex-direction: column;
  gap: 40rpx;
}

.diary-card {
  background: white;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  
  .diary-image-container {
    width: 100%;
    height: 400rpx;
    overflow: hidden;
    
    .diary-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
  
  .diary-content {
    padding: 32rpx;
    
    .diary-text {
      font-size: 32rpx;
      line-height: 1.6;
      color: #333;
      text-align: left;
      letter-spacing: 0.5rpx;
    }
  }
  
  .diary-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 32rpx 32rpx;
    
    .author-text {
      font-size: 28rpx;
      color: #666;
      font-weight: 500;
    }
    
    .date-text {
      font-size: 28rpx;
      color: #999;
    }
  }
}

.date-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.date-modal {
  background: white;
  border-radius: 24rpx;
  padding: 40rpx;
  margin: 0 40rpx;
  
  .picker-content {
    padding: 20rpx;
    text-align: center;
    
    text {
      font-size: 32rpx;
      color: #333;
    }
  }
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .header-nav {
    padding: 40rpx 24rpx 16rpx;
    
    .date-selector .date-text {
      font-size: 28rpx;
    }
  }
  
  .content-area {
    padding: 32rpx 24rpx;
  }
  
  .diary-card {
    .diary-image-container {
      height: 320rpx;
    }
    
    .diary-content {
      padding: 24rpx;
      
      .diary-text {
        font-size: 28rpx;
      }
    }
    
    .diary-footer {
      padding: 0 24rpx 24rpx;
      
      .author-text,
      .date-text {
        font-size: 24rpx;
      }
    }
  }
}
</style>