<script setup lang="ts">
import { ref, getCurrentInstance, onMounted } from 'vue'
import { http } from '@/utils/http'
import { onLoad } from "@dcloudio/uni-app";
import { useUserStore, useTokenStore } from '@/stores'
import { calculateDaysSinceRegistration } from '@/utils'
import wx from 'weixin-js-sdk' // 导入微信SDK
import { BubbleList, Typewriter } from 'vue-element-plus-x';

const WxJsSdk = getCurrentInstance()?.appContext.config.globalProperties.$WxJsSdk
const shareTitle = 'Skychart星图'
const shareInfo = '为你寻找完美的AI伙伴，陪你成长，一起建设星球！'
const shareUrl = 'https://lintu.tech/pages/ai/ai'
const shareImg = 'https://lintu.tech/static/ai/aibg.png'

// Get member info
const userStore = useUserStore()
const tokenStore = useTokenStore()

// Define types for list items
interface ChatItem {
  noStyle: boolean;
  key: number;
  role: 'user' | 'ai';
  placement: 'start' | 'end';
  content: string;
  loading: boolean;
  shape: string;
  variant: string;
  isMarkdown: boolean;
  typing: boolean;
  isFog: boolean;
  avatar: string;
  avatarSize: string;
  avatarGap: string;
  time: string;
}

// 任务项类型定义
interface TaskItem {
  id: number;
  icon: string;
  title: string;
  completed: boolean;
  difficulty: number; // 难度等级：1-3，对应闪电图标数量
}

const list = ref<ChatItem[]>([])

// 任务列表数据
const taskList = ref<TaskItem[]>([
  {
    id: 1,
    icon: '🌅',
    title: "今日的自我肯定",
    completed: true,
    difficulty: 1
  },
  {
    id: 2,
    icon: '👥',
    title: "查看这个观点...",
    completed: true,
    difficulty: 2
  },
  {
    id: 3,
    icon: '📱',
    title: "添加托兰小组件",
    completed: true,
    difficulty: 1
  },
  {
    id: 4,
    icon: '🎯',
    title: "探索你的性格档案",
    completed: true,
    difficulty: 3
  },
  {
    id: 5,
    icon: '👤',
    title: "你完全痴迷于什么？",
    completed: true,
    difficulty: 2
  }
])

// 控制任务列表弹窗显示
const showTaskModal = ref(false)

// 控制设置弹窗显示
const showSettingsModal = ref(false)

// 控制创建伙伴弹窗显示
const showPartnerModal = ref(false)
const partnerName = ref('')

onLoad( async (options?: any) => {
  if (options?.code) {
    const res = await http<any>({
        url:'/user/login?code=' + options.code
    })
    if (res.code == 200) {
        tokenStore.setToken(res.data.token)

        window.location.href = "https://lintu.tech/pages/ai/ai"

    } else {
        uni.showToast({
            title: res.msg,
            icon: 'none',
        })
    }
  }
});

onMounted(async () => {

  // 初始化微信SDK
  if (WxJsSdk) {
    WxJsSdk.init(shareTitle, shareInfo, shareUrl, shareImg)
    
    if(userStore.profile){
      // 使用导入的wx对象设置ready回调
      wx.ready(() => {
        console.log('微信SDK准备就绪，可以使用语音功能')
        isWxReady.value = true
      })
      
      wx.error((res) => {
        console.error('微信SDK初始化失败:', res)
        isWxReady.value = false
      })
    } else {
      console.log('未找到WxJsSdk')
      // 直接设置为可用状态
      isWxReady.value = true
    }
  }

  if (tokenStore.token) {
    const res = await http({ url:'/partner/info'})
    if (res.code == 200) {
      const res2 = await http({ url:'/initPartnerChat'})
        
      if (res2.code == 200) {
        const info = JSON.parse(res2.data.msg)
        console.log(info);
        
        for (let i = 0; i < info.length; i++) {
          if (info[i].type == 'AI') {
            list.value.push({
              noStyle: true,
              key: list.value.length + 1, // 唯一标识
              role: 'ai', // user | ai 自行更据模型定义
              placement: 'start', // start | end 气泡位置
              content: info[i].text, // 消息内容 流式接受的时候，只需要改这个值即可
              loading: false, // 当前气泡的加载状态
              shape: 'corner', // 气泡的形状
              variant: 'shadow', // 气泡的样式
              isMarkdown: true, // 是否渲染为 markdown
              typing: false, // 是否开启打字器效果 该属性不会和流式接受冲突
              isFog: true, // 是否开启打字雾化效果，该效果 v1.1.6 新增，且在 typing 为 true 时生效，该效果会覆盖 typing 的 suffix 属性
              avatar: userStore.profile.partner.style,
              avatarSize: '80rpx', // 头像占位大小
              avatarGap: '24rpx', // 头像与气泡之间的距离
              time: info[i].nowTime
            })
          } else if (info[i].type == 'USER') {
            list.value.push({
              noStyle: true,
              key: list.value.length + 1, // 唯一标识
              role: 'user', // user | ai 自行更据模型定义
              placement: 'end', // start | end 气泡位置
              content: info[i].contents[0].text, // 消息内容 流式接受的时候，只需要改这个值即可
              loading: false, // 当前气泡的加载状态
              shape: 'corner', // 气泡的形状
              variant: 'shadow', // 气泡的样式
              isMarkdown: false, // 是否渲染为 markdown
              typing: false, // 是否开启打字器效果 该属性不会和流式接受冲突
              isFog: false, // 是否开启打字雾化效果，该效果 v1.1.6 新增，且在 typing 为 true 时生效，该效果会覆盖 typing 的 suffix 属性
              avatar: userStore.profile ? userStore.profile?.avatar : '',
              avatarSize: '80rpx', // 头像占位大小
              avatarGap: '24rpx', // 头像与气泡之间的距离
              time: info[i].nowTime
            })
          }
        }

        console.log(list.value);
      }
    } else {
      const res1 = await http({ url:'/createChat'})
        
      if (res1.code == 200) {
        const info = JSON.parse(res1.data.msg)
        console.log(info);
        
        for (let i = 0; i < info.length; i++) {
          if (info[i].type == 'AI') {
            list.value.push({
              noStyle: true,
              key: list.value.length + 1, // 唯一标识
              role: 'ai', // user | ai 自行更据模型定义
              placement: 'start', // start | end 气泡位置
              content: info[i].text, // 消息内容 流式接受的时候，只需要改这个值即可
              loading: false, // 当前气泡的加载状态
              shape: 'corner', // 气泡的形状
              variant: 'shadow', // 气泡的样式
              isMarkdown: true, // 是否渲染为 markdown
              typing: false, // 是否开启打字器效果 该属性不会和流式接受冲突
              isFog: true, // 是否开启打字雾化效果，该效果 v1.1.6 新增，且在 typing 为 true 时生效，该效果会覆盖 typing 的 suffix 属性
              avatar: '/static/logo.png',
              avatarSize: '80rpx', // 头像占位大小
              avatarGap: '24rpx', // 头像与气泡之间的距离
              time: info[i].nowTime
            })
          } else if (info[i].type == 'USER') {
            list.value.push({
              noStyle: true,
              key: list.value.length + 1, // 唯一标识
              role: 'user', // user | ai 自行更据模型定义
              placement: 'end', // start | end 气泡位置
              content: info[i].contents[0].text, // 消息内容 流式接受的时候，只需要改这个值即可
              loading: false, // 当前气泡的加载状态
              shape: 'corner', // 气泡的形状
              variant: 'shadow', // 气泡的样式
              isMarkdown: false, // 是否渲染为 markdown
              typing: false, // 是否开启打字器效果 该属性不会和流式接受冲突
              isFog: false, // 是否开启打字雾化效果，该效果 v1.1.6 新增，且在 typing 为 true 时生效，该效果会覆盖 typing 的 suffix 属性
              avatar: userStore.profile ? userStore.profile?.avatar : '',
              avatarSize: '80rpx', // 头像占位大小
              avatarGap: '24rpx', // 头像与气泡之间的距离
              time: info[i].nowTime
            })
          }
        }

        console.log(list.value);
      }
    }
  }
})

const longerValue = ref<string>('')

const send = async () => {
  if(!userStore.profile){
    login()
    return
  }
  
  if (longerValue.value == '') {
        uni.showToast({
            icon: 'none',
            title: '请输入内容'
        })
        return
    }
  
  list.value.push({
    noStyle: true,
    key: list.value.length + 1, // 唯一标识
    role: 'user', // user | ai 自行更据模型定义
    placement: 'end', // start | end 气泡位置
    content: longerValue.value, // 消息内容 流式接受的时候，只需要改这个值即可
    loading: false, // 当前气泡的加载状态
    shape: 'corner', // 气泡的形状
    variant: 'shadow', // 气泡的样式
    isMarkdown: false, // 是否渲染为 markdown
    typing: false, // 是否开启打字器效果 该属性不会和流式接受冲突
    isFog: false, // 是否开启打字雾化效果，该效果 v1.1.6 新增，且在 typing 为 true 时生效，该效果会覆盖 typing 的 suffix 属性
    avatar: userStore.profile ? userStore.profile.avatar : '',
    avatarSize: '80rpx', // 头像占位大小
    avatarGap: '24rpx', // 头像与气泡之间的距离
    time: new Date().toLocaleString()
  })

    let prompt = longerValue.value
    longerValue.value = ''
    uni.showLoading({
        title: '天空守护者思考中',
        mask: true
    })
    const res = await http({
      method: 'POST',
      url:'/ai/aiInitialMatching',
      data: {
        prompt: prompt
      },
      header: {
        'content-type': 'application/x-www-form-urlencoded'
      }
    })
    if (res.code == 200) {
        uni.hideLoading()
        // memoryId.value = res.data.id
        list.value.push({
          noStyle: true,
          key: list.value.length + 1, // 唯一标识
          role: 'ai', // user | ai 自行更据模型定义
          placement: 'start', // start | end 气泡位置
          content: res.data as string, // 消息内容 流式接受的时候，只需要改这个值即可
          loading: false, // 当前气泡的加载状态
          shape: 'corner', // 气泡的形状
          variant: 'shadow', // 气泡的样式
          isMarkdown: true, // 是否渲染为 markdown
          typing: true, // 是否开启打字器效果 该属性不会和流式接受冲突
          isFog: true, // 是否开启打字雾化效果，该效果 v1.1.6 新增，且在 typing 为 true 时生效，该效果会覆盖 typing 的 suffix 属性
          avatar: '/static/logo.png',
          avatarSize: '80rpx', // 头像占位大小
          avatarGap: '24rpx', // 头像与气泡之间的距离
          time: new Date().toLocaleString()
        })
    } else {
        uni.hideLoading()
        uni.showToast({
          title: res.msg,
          icon: 'none'
        })
    }
}

const copy = (content: string) => {
  uni.setClipboardData({
    data: content,
    success: () => {
    },
    fail: () => {
    }
  })
}

//监听天空守护者聊天结束
const complete = (instance: any) => { // Add any type if unknown
  if (list.value.length == 9) {
    uni.showLoading({
        title: '',
        mask: true
    })
    setTimeout(() => {
      list.value.push({
        noStyle: true,
        key: list.value.length + 1, // 唯一标识
        role: 'ai', // user | ai 自行更据模型定义
        placement: 'start', // start | end 气泡位置
        content: '我已聆听你灵魂的回响，此刻，让星辉为你凝成独一无二的星灵。', // 消息内容 流式接受的时候，只需要改这个值即可
        loading: false, // 当前气泡的加载状态
        shape: 'corner', // 气泡的形状
        variant: 'shadow', // 气泡的样式
        isMarkdown: true, // 是否渲染为 markdown
        typing: true, // 是否开启打字器效果 该属性不会和流式接受冲突
        isFog: true, // 是否开启打字雾化效果，该效果 v1.1.6 新增，且在 typing 为 true 时生效，该效果会覆盖 typing 的 suffix 属性
        avatar: '/static/logo.png',
        avatarSize: '80rpx', // 头像占位大小
        avatarGap: '24rpx', // 头像与气泡之间的距离
        time: new Date().toLocaleString()
      })
      uni.hideLoading()
      setTimeout(() => {
        // 显示创建伙伴弹窗
        showPartnerModal.value = true
      },3000)
    },1500)
  }
}

// New functions for UI
const showPopup = ref(false)
const showChatInput = ref(false)
const togglePopup = () => {
  if(!userStore.profile){
    login()
    return
  }
  showPopup.value = !showPopup.value
}

const onPhoto = () => {
  if(!userStore.profile){
    login()
    return
  }
  // Implement photo send logic
  uni.chooseImage({
    count: 1,
    success: (res) => {
      // Send photo to AI or handle
    }
  })
  showPopup.value = false
}

const onLink = () => {
  if(!userStore.profile){
    login()
    return
  }
  // Paste link logic
  showPopup.value = false
}

const onReminder = () => {
  if(!userStore.profile){
    login()
    return
  }
  // Set reminder logic
  showPopup.value = false
}

const toggleChat = () => {
  if(!userStore.profile){
    login()
    return
  }
  // Show/hide chat list
  showChatInput.value = !showChatInput.value
  if (showChatInput.value) {
    showPopup.value = false // 关闭popup如果打开了
  }
}

const hideChatInput = () => {
  showChatInput.value = false
}

const handleContainerTap = () => {
  // 由于chat-input-bar已经有@tap.stop，这里应该不会被触发
  // 但为了保险起见，我们延迟执行以确保其他事件处理完成
  if (showChatInput.value) {
    setTimeout(() => {
      if (showChatInput.value) {
        hideChatInput()
      }
    }, 10)
  }
}

const cancel = () => {
  longerValue.value = ''
  showChatInput.value = false
}

// 任务相关功能
const toggleTaskModal = () => {
  if(!userStore.profile){
    login()
    return
  }
  showTaskModal.value = !showTaskModal.value
  // 关闭其他弹窗
  showPopup.value = false
  showChatInput.value = false
}

const toggleTaskComplete = (taskId: number) => {
  if(!userStore.profile){
    login()
    return
  }
  const task = taskList.value.find(t => t.id === taskId)
  if (task) {
    task.completed = !task.completed
  }
}

const addNewTask = () => {
  if(!userStore.profile){
    login()
    return
  }
  uni.showModal({
    title: '添加新任务',
    editable: true,
    placeholderText: '请输入任务内容',
    success: (res) => {
      if (res.confirm && res.content) {
        const newTask: TaskItem = {
          id: Date.now(),
          icon: '📝',
          title: res.content,
          completed: false,
          difficulty: 1 // 默认难度为1
        }
        taskList.value.push(newTask)
      }
    }
  })
}

// 设置弹窗相关功能
const toggleSettingsModal = () => {
  showSettingsModal.value = !showSettingsModal.value
  // 关闭其他弹窗
  showPopup.value = false
  showChatInput.value = false
  showTaskModal.value = false
}

// 设置选项处理函数
const onNotebook = () => {
  uni.navigateTo({
    url: '/pages/diary/diary'
  })
  showSettingsModal.value = false
}

const onFriends = () => {
  uni.showToast({
    title: '朋友功能开发中',
    icon: 'none'
  })
  showSettingsModal.value = false
}

const onStyle = () => {
  uni.navigateTo({
    url: '/pages/style/style'
  })
  showSettingsModal.value = false
}

const onPersonality = () => {
  uni.navigateTo({
    url: '/pages/personality/personality'
  })
  showSettingsModal.value = false
}

const onProfile = () => {
  uni.navigateTo({
    url: '/pages/profile/profile'
  })
  showSettingsModal.value = false
}

const onLore = () => {
  uni.showToast({
    title: '世界观开发中',
    icon: 'none'
  })
  showSettingsModal.value = false
}

const onWidget = () => {
  uni.showToast({
    title: '小组件开发中',
    icon: 'none'
  })
  showSettingsModal.value = false
}

const onFeedback = () => {
  uni.showToast({
    title: '意见反馈功能开发中',
    icon: 'none'
  })
  showSettingsModal.value = false
}

const onRanking = () => {
  uni.navigateTo({
    url: '/pages/ranking/ranking'
  })
  showSettingsModal.value = false
}

const onExchange = () => {
  uni.navigateTo({
    url: '/pages/exchange/exchange'
  })
  showSettingsModal.value = false
}

// 判断是否为开发环境
const isDev = import.meta.env.DEV || window.location.hostname === 'localhost'

const login = () => {

  if(userStore.profile){
    return
  }else{
  uni.showModal({
    title: '登录',
    content: '是否使用微信登录，获取你的头像和昵称作为初始信息。',
    success: async (res) => {
      if (res.confirm) {
        if (isDev) {
          // 开发环境：使用模拟登录
          console.log('开发环境，使用模拟登录')
          const res = await http<any>({
              url: '/user/mock-login'
          })
          if (res.code == 200) {
              tokenStore.setToken(res.data.token)
              
              window.location.href = "http://localhost:5173/pages/ai/ai"
          } else {
              uni.showToast({
                  title: res.msg || '模拟登录失败',
                  icon: 'none',
              })
          }
      } else {
          // 生产环境：使用微信授权登录
          console.log('生产环境，使用微信授权登录')
          const res = await http<any>({
              url: '/user/url?url=https://lintu.tech/pages/ai/ai/'
          })
          if (res.code == 200) {
              window.location.href = res.data
          } else {
              uni.showToast({
                  title: res.msg || '获取授权链接失败',
                  icon: 'none',
              })
          }
      }
      }
    }
  })
}
}

// 退出登录
const logout = () => {
  uni.showModal({
    title: '提示',
    content: '确定要退出登录吗？',
    success: (res) => {
      if (res.confirm) {
        // 清除本地存储的用户信息
        tokenStore.clearToken()
        userStore.clearProfile()
        uni.showToast({
          title: '已退出登录',
          icon: 'none',
        })
        list.value = []
        showSettingsModal.value = false
      }
    }
  })
}

const toPlanet = () => {
  uni.navigateTo({
    url: '/pages/planet/planet'
  })
}

// 创建伙伴相关功能
const togglePartnerModal = () => {
  // 只在打开弹窗时检查登录状态，关闭时不检查
  if (!showPartnerModal.value && !userStore.profile) {
    login()
    return
  }
  showPartnerModal.value = !showPartnerModal.value
  // 关闭其他弹窗
  showPopup.value = false
  showChatInput.value = false
  showTaskModal.value = false
  showSettingsModal.value = false
}

const confirmCreatePartner = async () => {
  if (!partnerName.value.trim()) {
    uni.showToast({
      title: '请输入昵称',
      icon: 'none'
    })
    return
  }
  
  try {
    uni.showLoading({
      title: '正在创建星灵...',
      mask: true
    })
    
    const res = await http({
      method: 'POST',
      url: '/ai/creator',
      data: {
        nickname: partnerName.value
      },
      header: {
        'content-type': 'application/x-www-form-urlencoded'
      }
    })
    
    // 更新用户信息，添加伙伴
    if (res.code == 200) {
      const res3 = await http<any>({
                  url: '/role/info'
              })
              
              if (res3.code == 200) {
                if (userStore.profile) {
                  userStore.setProfile({
                    ...userStore.profile,
                    role: {
                      traits: res3.data.traits,
                      description: res3.data.description,
                      attribute: {
                        spontaneous: res3.data.spontaneous,
                        collaborative: res3.data.collaborative,
                        realist: res3.data.realist,
                        logical: res3.data.logical,
                        analytical: res3.data.analytical,
                        introvert: res3.data.introvert
                      }
                    }
                  })
                }
              }

              const res2 = await http<any>({
                  url: '/partner/info'
              })
              
              if (res2.code == 200) {
                if (userStore.profile) {
                  userStore.setProfile({
                    ...userStore.profile,
                    partner: {
                      nickName: res2.data.nickname,
                      style: res2.data.style,
                      traits: res2.data.traits,
                      description: res2.data.description,
                      createTime: res2.data.createTime,
                      attribute: {
                        spontaneous: res2.data.spontaneous,
                        collaborative: res2.data.collaborative,
                        realist: res2.data.realist,
                        logical: res2.data.logical,
                        analytical: res2.data.analytical,
                        introvert: res2.data.introvert
                      }
                    }
                  })
                }
              }
      uni.hideLoading()
      uni.showToast({
        title: '星灵创建成功！',
        icon: 'success'
      })
      
      list.value = []
      showPartnerModal.value = false
      partnerName.value = ''
    }
  } catch (error) {
    uni.hideLoading()
    uni.showToast({
      title: '创建失败，请重试',
      icon: 'none'
    })
  }
}

const isWxReady = ref(false)
// 添加语音相关状态和方法
const senderRef = ref()
const isRecording = ref(false)
const currentLocalId = ref('')

// 开始语音识别
const startRecording = async () => {
  if (isRecording.value) return // 防止重复触发
  
  // 检查微信SDK是否准备好
  if (!isWxReady.value) {
    uni.showToast({
      icon: 'none',
      title: '语音功能正在初始化，请稍后再试',
      duration: 2000
    })
    return
  }
  
  try {
    isRecording.value = true
    uni.showToast({
      icon: 'none',
      title: '长按录音中...',
      duration: 1000
    })
    
    // 使用微信录音接口
    wx.startRecord()
    console.log('已调用wx.startRecord()')
    
    // 监听录音自动停止（超过1分钟）
    wx.onVoiceRecordEnd({
      complete: function (res) {
        console.log('录音自动停止:', res)
        currentLocalId.value = res.localId
        isRecording.value = false
        translateVoice(res.localId)
      }
    })
    
  } catch (error) {
    console.error('启动录音失败:', error)
    isRecording.value = false
    uni.showToast({
      icon: 'none',
      title: '录音启动失败',
      duration: 2000
    })
  }
}

// 停止语音识别
const stopRecording = async () => {
  if (!isRecording.value) return // 如果没有在录音，直接返回
  
  // 检查微信SDK是否准备好
  if (!isWxReady.value) {
    isRecording.value = false
    uni.showToast({
      icon: 'none',
      title: '语音功能异常',
      duration: 1500
    })
    return
  }
  
  try {
    isRecording.value = false
    uni.showToast({
      icon: 'none',
      title: '语音处理中...',
      duration: 1000
    })
    
    // 使用微信停止录音接口
    wx.stopRecord({
      success: function (res) {
        console.log('录音成功，localId:', res.localId)
        currentLocalId.value = res.localId
        // 立即开始语音识别
        translateVoice(res.localId)
      },
      fail: function (err) {
        console.error('停止录音失败:', err)
        uni.showToast({
          icon: 'none',
          title: '录音失败',
          duration: 1500
        })
      }
    })
    
  } catch (error) {
    console.error('停止录音失败:', error)
    isRecording.value = false
    uni.showToast({
      icon: 'none',
      title: '录音失败',
      duration: 1500
    })
  }
}

// 微信语音识别（简化版本）
const translateVoice = (localId) => {
  if (!localId) {
    uni.showToast({
      icon: 'none',
      title: '录音数据异常',
      duration: 1500
    })
    return
  }
  
  // 检查微信SDK是否准备好
  if (!isWxReady.value) {
    uni.showToast({
      icon: 'none',
      title: '语音识别功能异常',
      duration: 1500
    })
    return
  }
  
  try {
    wx.translateVoice({
      localId: localId,
      isShowProgressTips: 1,
      success: function (res) {
        console.log('语音识别成功:', res)
        const result = res.translateResult
        if (result && result.trim()) {
          handleSpeechResult(result)
        } else {
          uni.showToast({
            icon: 'none',
            title: '未识别到语音内容',
            duration: 1500
          })
        }
      },
      fail: function (err) {
        console.error('语音识别失败:', err)
        uni.showToast({
          icon: 'none',
          title: '语音识别失败',
          duration: 1500
        })
      }
    })
  } catch (error) {
    console.error('调用语音识别失败:', error)
    uni.showToast({
      icon: 'none',
      title: '语音识别异常',
      duration: 1500
    })
  }
}

// 语音识别结果处理
const handleSpeechResult = async (result) => {
  if (result && result.trim()) {
    
    if(result.trim().length > 0){
      list.value.push({
        noStyle: true,
        key: list.value.length + 1, // 唯一标识
        role: 'user', // user | ai 自行更据模型定义
        placement: 'end', // start | end 气泡位置
        content: result.trim(), // 消息内容 流式接受的时候，只需要改这个值即可
        loading: false, // 当前气泡的加载状态
        shape: 'corner', // 气泡的形状
        variant: 'shadow', // 气泡的样式
        isMarkdown: false, // 是否渲染为 markdown
        typing: false, // 是否开启打字器效果 该属性不会和流式接受冲突
        isFog: false, // 是否开启打字雾化效果，该效果 v1.1.6 新增，且在 typing 为 true 时生效，该效果会覆盖 typing 的 suffix 属性
        avatar: userStore.profile ? userStore.profile.avatar : '',
        avatarSize: '80rpx', // 头像占位大小
        avatarGap: '24rpx', // 头像与气泡之间的距离
        time: new Date().toLocaleString()
      })

      uni.showLoading({
          title: '天空守护者思考中',
          mask: true
      })
      const res = await http({
        method: 'POST',
        url:'/ai/aiInitialMatching',
        data: {
          prompt: result.trim()
        },
        header: {
          'content-type': 'application/x-www-form-urlencoded'
        }
      })
      if (res.code == 200) {
          uni.hideLoading()
          list.value.push({
            noStyle: true,
            key: list.value.length + 1, // 唯一标识
            role: 'ai', // user | ai 自行更据模型定义
            placement: 'start', // start | end 气泡位置
            content: res.data as string, // 消息内容 流式接受的时候，只需要改这个值即可
            loading: false, // 当前气泡的加载状态
            shape: 'corner', // 气泡的形状
            variant: 'shadow', // 气泡的样式
            isMarkdown: true, // 是否渲染为 markdown
            typing: true, // 是否开启打字器效果 该属性不会和流式接受冲突
            isFog: true, // 是否开启打字雾化效果，该效果 v1.1.6 新增，且在 typing 为 true 时生效，该效果会覆盖 typing 的 suffix 属性
            avatar: '/static/logo.png',
            avatarSize: '80rpx', // 头像占位大小
            avatarGap: '24rpx', // 头像与气泡之间的距离
            time: new Date().toLocaleString()
          })
      } else {
          uni.hideLoading()
          uni.showToast({
            title: res.msg,
            icon: 'none'
          })
      }
    } else {
      uni.showToast({
        icon: 'none',
        title: '语音太短',
      })
      return
    }
    
    uni.showToast({
      icon: 'success',
      title: '语音转换成功',
      duration: 1500
    })
  } else {
    uni.showToast({
      icon: 'none',
      title: '未识别到语音内容',
      duration: 1500
    })
  }
}

// 长按开始录音的处理
const handleTouchStart = (event) => {
  event.preventDefault() // 防止默认行为
  
  console.log('触发长按，当前状态:', {
    isWxReady: isWxReady.value,
    wxStartRecord: typeof wx?.startRecord,
    wxTranslateVoice: typeof wx?.translateVoice
  })
  
  startRecording()
}

// 松手结束录音的处理  
const handleTouchEnd = (event) => {
  event.preventDefault() // 防止默认行为
  
  if (isRecording.value) {
    stopRecording()
  }
}
</script>

<template>
  <view class="ai-container" @tap="handleContainerTap">
    <!-- Top Bar -->
    <view class="top-bar" style="display: flex; justify-content: space-between; align-items: center; padding: 40rpx; position: relative; z-index: 100;">
      <image src="/static/ai/planet.png" style="width: 60rpx; height: 60rpx;" @tap.stop="toPlanet" /> <!-- Planet icon -->
      <view class="task-icon-container" @tap.stop="toggleTaskModal">
        <view class="task-activity-ring">
          <view class="task-ring-progress"></view>
        </view>
        <image src="/static/ai/tasks.png" style="width: 80rpx; height: 80rpx;" />
      </view>
      <image v-if="userStore.profile" :src="userStore.profile.avatar" style="width: 70rpx; height: 70rpx; border-radius: 50%;" @tap.stop="toggleSettingsModal" /> <!-- User avatar -->
      <image v-else src="/static/ai/login.png" style="width: 60rpx; height: 60rpx; border-radius: 50%;" @tap.stop="login" /> <!-- User avatar -->
    </view>
    
    <!-- Central AI Character - 微动 -->
    <view style="position: absolute;top: 0;left: 0;right: 0;bottom: 0;display: flex;justify-content: center;align-items: center; z-index: 1;">
      <view class="rabbit-container">
        <image v-if="!userStore.profile?.partner" src="/static/ai/egg.png" class="animated-rabbit" style="width: 600rpx;" model="widthFix" />
        <image v-else :src="userStore.profile?.partner.style" class="animated-rabbit" style="width: 600rpx;margin-top: 20%" mode="widthFix" />
      </view>
    </view>
    
    <!-- 聊天气泡列表 -->
    <BubbleList :list="list" style="width: 90%;z-index: 10;height: 80%;margin: 0 auto;" >
      <!-- 自定义头像 -->
      <template #avatar="{ item }">
        <view style="width: 80rpx;height: 80rpx;border-radius: 50%;" v-if="item.role === 'ai'">
          <image style="width: 100%;height: 100%;border-radius: 50%;" src="/static/logo.png" mode="aspectFill" />
        </view>
        <view style="width: 80rpx;height: 80rpx;border-radius: 50%;" v-else>
          <image style="width: 100%;height: 100%;border-radius: 50%;" :src="userStore.profile ? userStore.profile.avatar : ''" mode="aspectFill" />
        </view>
      </template>
      <!-- 自定义头部 -->
      <template #header="{ item }">
        <view class="header-wrapper">
          <view class="header-name">
            <view style="display: flex;align-items: center;justify-content: space-between;" v-if="item.role === 'ai'">
              <view style="color: #cdcdcd;">天空守护者</view>
              <view style="color: #cdcdcd;margin-left: 20rpx;">
                {{ item.time }}
              </view>
            </view>
            <view style="display: flex;align-items: center;justify-content: space-between;" v-else>
              <view style="color: #cdcdcd;">
                {{ item.time }}
              </view>
              <view style="margin-left: 20rpx;color: #fff;">{{ userStore.profile ? userStore.profile.nickname : '' }}</view>
            </view>
          </view>
        </view>
      </template>
      <!-- 自定义内容 -->
      <template #content="{ item }">
        <view :style="{ 
          background: '#fff', 
          padding: '12px', 
          borderRadius: '10rpx', 
          marginTop: '10rpx', 
          width: '75%',
          marginLeft: item.role === 'user' ? 'auto' : '0',
          marginRight: item.role === 'ai' ? '' : 'auto'
        }">
          <view v-if="item.role === 'ai'">
            <Typewriter v-if="list.length == item.key" @finish="complete" :content="item.content" :is-markdown="true" is-fog typing />
            <view v-else>{{ item.content }}</view>
          </view>
          <view v-else>{{ item.content }}</view>
        </view>
      </template>
      <!-- 自定义底部 -->
      <!-- <template #footer="{ item }">
        <view class="footer-wrapper">
          <view class="footer-container">
            <image
              @tap="copy(item.content)"
              src="/static/images/copy.png"
              mode="aspectFit"
              style="width: 40rpx;height: 40rpx;"
            />
          </view>
        </view>
      </template> -->
    </BubbleList>
    
    <!-- 圆形启动按钮 -->
    <view v-if="!userStore.profile" class="start-game-btn" @tap.stop="login">
      <view class="start-btn-circle">
        <text class="start-btn-text">启动</text>
      </view>
    </view>
    
    <!-- 底部栏 -->
    <view v-if="!showChatInput && userStore.profile" class="bottom-bar">
      <view v-if="!showPopup" class="plus-btn" @tap.stop="togglePopup">
        <image
          src="/static/ai/plus.png"
          mode="aspectFit"
          style="width: 40rpx; height: 40rpx;"
        />
      </view>
      <view v-else class="plus-btn" @tap.stop="togglePopup">
        <image
          src="/static/ai/cancel1.png"
          mode="aspectFit"
          style="width: 40rpx; height: 40rpx;"
        />
      </view>
      <view class="action-btns">
        <view class="voice-btn" @tap.stop="toggleChat">
          <image src="/static/ai/chat.png" mode="aspectFit" style="width: 60rpx; height: 60rpx;" />
        </view>
        <view class="voice-btn"
          :class="{ 
            'voice-recording': isRecording,
            'voice-ready': isWxReady && !isRecording,
            'voice-disabled': !isWxReady
          }"
          @touchstart="handleTouchStart"
          @touchend="handleTouchEnd"
          @touchcancel="handleTouchEnd"
          @mousedown="handleTouchStart" 
          @mouseup="handleTouchEnd"
          @mouseleave="handleTouchEnd">
          <image 
            :src="'/static/ai/mike.png'" 
            mode="aspectFit" 
            :style="{ 
              width: '50rpx', 
              height: '50rpx',
              opacity: isRecording ? '0.8' : '1',
              transform: isRecording ? 'scale(1.1)' : 'scale(1)'
            }" 
          />
          <view v-if="isRecording" class="recording-indicator">
          </view>
          <view v-else-if="!isWxReady" class="voice-status-indicator">
          </view>
        </view>
      </view>
    </view>
    
    <!-- 聊天输入框 -->
    <view v-if="showChatInput" class="chat-input-bar" @tap.stop>
      <view class="input-container">
        <input 
          v-model="longerValue" 
          placeholder="说点什么..." 
          class="chat-input"
          @confirm="send"
        />
        <view class="cancel-btn" @tap.stop="cancel">
          <image
            src="/static/ai/cancel.png"
            mode="scaleToFill"
            style="width: 60rpx; height: 60rpx;"
          />
        </view>
      </view>
      <view class="send-btn" @tap.stop="send">
        <image
          src="/static/ai/send.png"
          mode="scaleToFill"
          style="width: 70rpx; height: 70rpx;"
        />
      </view>
    </view>
    
    <!-- 加号弹窗 -->
    <view v-if="showPopup" class="popup-overlay" @tap="togglePopup">
      <view class="popup-content" @tap.stop>
        <view class="popup-option" @tap="onPhoto">
          <view class="option-icon">
            <text>📷</text>
          </view>
          <text class="option-text">发送照片</text>
        </view>
        <view class="popup-option" @tap="onLink">
          <view class="option-icon">
            <text>🔗</text>
          </view>
          <text class="option-text">发送链接</text>
        </view>
        <view class="popup-option" @tap="onReminder">
          <view class="option-icon">
            <text>⏰</text>
          </view>
          <text class="option-text">设置提醒</text>
        </view>
      </view>
    </view>
    
    <!-- 任务列表弹窗 -->
    <view v-if="showTaskModal" class="task-modal-overlay" @tap="toggleTaskModal">
      <view class="task-modal" @tap.stop>
        <!-- 任务列表头部 -->
        <view class="task-header">
          <view class="task-header-left">
            <text class="task-icon">今天</text>
            <text class="task-title">{{ new Date().toLocaleDateString() }}</text>
          </view>
          <view class="task-header-right" @tap="addNewTask">
            <image
              src="/static/ai/lightning.png"
              mode="scaleToFill"
              style="width: 40rpx; height: 40rpx;"
            />
            <text class="add-task-btn">{{ userStore.profile ? userStore.profile.tokens : 0 }}</text>
          </view>
        </view>
        
        <!-- 任务列表 -->
        <view class="task-list">
          <view 
            v-for="task in taskList" 
            :key="task.id" 
            class="task-item"
            @tap="toggleTaskComplete(task.id)"
          >
            <view class="task-content">
              <text class="task-emoji">{{ task.icon }}</text>
              <text class="task-text">{{ task.title }}</text>
            </view>
            <view class="task-check">
              <image
                v-for="i in task.difficulty"
                :key="i"
                src="/static/ai/lightning.png"
                mode="scaleToFill"
                style="width: 40rpx; height: 40rpx; margin-left: 8rpx;"
              />
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 设置弹窗 -->
    <view v-if="showSettingsModal" class="settings-modal-overlay" @tap="toggleSettingsModal">
      <view class="settings-modal" @tap.stop>
        <!-- 设置弹窗头部 -->
        <view class="settings-header">
          <view class="settings-header-left">
            <!-- <text class="settings-icon">⚙️</text> -->
            <view class="settings-title">{{ userStore.profile ? userStore.profile.nickname : '' }}{{ userStore.profile?.partner ? ' & ' + userStore.profile.partner.nickName : ' ' }}</view>
            <view style="color: #666;font-size: 24rpx;">{{ userStore.profile?.partner ? '羁绊建立于' + userStore.profile.partner.createTime : '' }}</view>
          </view>
        </view>

        <view v-if="userStore.profile?.partner" style="color: #374151;font-size: 28rpx;text-align: center;display: flex;align-items: center;justify-content: center;margin: 10rpx 0">
          <image
            src="/static/ai/hot.png"
            mode="scaleToFill"
            style="width: 40rpx; height: 40rpx;"
          />
          友谊小船 {{ userStore.profile?.partner.createTime ? calculateDaysSinceRegistration(userStore.profile.partner.createTime) : 0 }}天
        </view>
        
        <!-- 设置选项列表 -->
        <view class="settings-list">

          <view class="settings-item" @tap="onProfile">
            <view class="settings-content">
              <text class="settings-emoji">👤</text>
              <text class="settings-text">个人资料</text>
              <image :src="userStore.profile ? userStore.profile.avatar : ''" mode="scaleToFill" style="width: 50rpx; height: 50rpx;border-radius: 10rpx;" />
            </view>
          </view>
          
          <view class="settings-item" @tap="onStyle">
            <view class="settings-content">
              <text class="settings-emoji">🎨</text>
              <text class="settings-text">风格</text>
              <image :src="userStore.profile?.partner ? userStore.profile?.partner.style : ''" mode="scaleToFill" style="width: 50rpx; height: 50rpx;border-radius: 10rpx;" />
            </view>
          </view>
          
          <view class="settings-item" @tap="onPersonality">
            <view class="settings-content">
              <text class="settings-emoji">🧠</text>
              <text class="settings-text">性格</text>
              <image src="https://img1.baidu.com/it/u=3548239810,287346742&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=667" mode="scaleToFill" style="width: 50rpx; height: 50rpx;border-radius: 10rpx;" />
            </view>
          </view>

          <view class="settings-item" @tap="onNotebook">
            <view class="settings-content">
              <text class="settings-emoji">📔</text>
              <text class="settings-text">日记</text>
              <image src="/static/diary1.png" mode="scaleToFill" style="width: 50rpx; height: 50rpx;border-radius: 10rpx;margin-left: 20rpx" />
              <image src="/static/diary2.png" mode="scaleToFill" style="width: 50rpx; height: 50rpx;border-radius: 10rpx;margin-left: 20rpx" />
            </view>
          </view>

          <view class="settings-item" @tap="onFriends">
            <view class="settings-content">
              <text class="settings-emoji">👥</text>
              <text class="settings-text">好友</text>
              <image src="/static/test1.png" mode="scaleToFill" style="width: 50rpx; height: 50rpx;border-radius: 10rpx;margin-left: 20rpx" />
            </view>
          </view>
          
          <!-- <view class="settings-item" @tap="onLore">
            <view class="settings-content">
              <text class="settings-emoji">🌍</text>
              <text class="settings-text">世界观</text>
            </view>
          </view> -->

          <view class="settings-item" @tap="onRanking">
            <view class="settings-content">
              <text class="settings-emoji">🏆</text>
              <text class="settings-text">排行榜</text>
            </view>
          </view>

          <view class="settings-item" @tap="onExchange">
            <view class="settings-content">
              <text class="settings-emoji">💬</text>
              <text class="settings-text">社区交流</text>
            </view>
          </view>
          
          <!-- <view class="settings-item" @tap="onWidget">
            <view class="settings-content">
              <text class="settings-emoji">📱</text>
              <text class="settings-text">小组件</text>
            </view>
          </view> -->
          
          <view class="settings-item" @tap="onFeedback" style="border-bottom: 1rpx solid #e5e7eb;">
            <view class="settings-content">
              <text class="settings-emoji">❓</text>
              <text class="settings-text">意见反馈</text>
            </view>
          </view>
        </view>

        <view @tap="logout" style="color: #374151;font-size: 28rpx;text-align: center;display: flex;align-items: center;justify-content: center;margin-top: 30rpx;">
          🚪退出登录
        </view>
      </view>
    </view>
    
    <!-- 创建伙伴弹窗 -->
    <view v-if="showPartnerModal" class="partner-modal-overlay">
      <view class="partner-modal" @tap.stop>
        <!-- 弹窗头部 -->
        <view class="partner-header">
          <view class="partner-title">✨ 为你的星灵取个名字</view>
          <view class="partner-subtitle">这将是你们专属的称呼</view>
        </view>
        
        <!-- 输入框 -->
        <view class="partner-input-container">
          <input 
            v-model="partnerName" 
            placeholder="输入伙伴昵称..." 
            class="partner-input"
            maxlength="10"
            @confirm="confirmCreatePartner"
          />
        </view>
        
        <!-- 按钮组 -->
        <view class="partner-buttons">
          <!-- <view class="partner-btn cancel-btn" @tap="togglePartnerModal">
            取消
          </view> -->
          <view class="partner-btn confirm-btn" @tap="confirmCreatePartner">
            确认
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.ai-container{
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  background: url('/static/ai/aibg.png') no-repeat center center;
  background-size: 100% 100%;
  color: white;
}
/* 底部按钮栏样式 */
.bottom-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40rpx;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 999;
  box-sizing: border-box;
}

.plus-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-left: 20rpx;
  transition: all 0.2s ease;
  padding: 10rpx;
}

.plus-btn:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.3);
}

.action-btns {
  display: flex;
  align-items: center;
}

.voice-btn {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-left: 40rpx;
  transition: all 0.2s ease;
  padding: 10rpx;
}

.voice-btn:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.2);
}

/* 语音按钮状态样式 */
.voice-ready {
  background: rgba(76, 175, 80, 0.1);
  border: 2rpx solid rgba(76, 175, 80, 0.3);
}

.voice-recording {
  background: rgba(244, 67, 54, 0.2);
  border: 2rpx solid rgba(244, 67, 54, 0.5);
  animation: recordingPulse 1s ease-in-out infinite;
}

.voice-disabled {
  background: rgba(158, 158, 158, 0.1);
  border: 2rpx solid rgba(158, 158, 158, 0.3);
  opacity: 0.6;
}

.recording-indicator {
  position: absolute;
  bottom: -40rpx;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(244, 67, 54, 0.9);
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  white-space: nowrap;
  box-shadow: 0 4rpx 12rpx rgba(244, 67, 54, 0.3);
}

.recording-text {
  font-size: 24rpx;
  font-weight: 500;
}

.voice-status-indicator {
  position: absolute;
  bottom: -40rpx;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(158, 158, 158, 0.9);
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 20rpx;
  white-space: nowrap;
  box-shadow: 0 4rpx 12rpx rgba(158, 158, 158, 0.3);
}

.status-text {
  font-size: 20rpx;
  font-weight: 400;
}

@keyframes recordingPulse {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(244, 67, 54, 0.7);
  }
  50% {
    box-shadow: 0 0 0 20rpx rgba(244, 67, 54, 0);
  }
}

.chat-input-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 40rpx;
  display: flex;
  align-items: center;
  gap: 20rpx;
  z-index: 1000;
  box-sizing: border-box;
}

.input-container {
  flex: 1;
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 50rpx;
  padding: 10rpx 20rpx 10rpx 30rpx;
  z-index: 1001;
  position: relative;
}

.chat-input {
  flex: 1;
  border: none;
  outline: none;
  background: transparent;
  font-size: 32rpx;
  color: #333;
}

.chat-input::placeholder {
  color: #999;
}

.cancel-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  margin-left: 10rpx;
  cursor: pointer;
  z-index: 1001;
}

.cancel-btn:active {
  transform: scale(0.9);
}

.send-btn {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  cursor: pointer;
  z-index: 1001;
}

.send-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 10rpx rgba(102, 126, 234, 0.3);
}

.popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  background-color: rgba(0, 0, 0, 0.3);
}

.popup-content {
  position: absolute;
  bottom: 120rpx;
  left: 20rpx;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.popup-option {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(15px);
  border-radius: 60rpx;
  padding: 12rpx 36rpx;
  min-width: 320rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
  transition: all 0.2s ease;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.popup-option:active {
  transform: scale(0.96);
  background: rgba(255, 255, 255, 0.85);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.option-icon {
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  font-size: 32rpx;
}

.option-text {
  font-size: 32rpx;
  color: #000;
  font-weight: 500;
}

// 任务弹窗样式
.task-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  background-color: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(8rpx);
  display: flex;
  align-items: center;
  justify-content: center;
}

.task-modal {
  width: 90%;
  max-width: 600rpx;
  max-height: 80vh;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 32rpx;
  padding: 40rpx;
  box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.15);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
}

.task-header-left {
  display: flex;
  align-items: center;
}

.task-icon {
  font-size: 48rpx;
  margin-right: 16rpx;
  color: #000;
}

.task-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #999;
}

.task-header-right {
  padding: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.add-task-btn {
  font-size: 40rpx;
  cursor: pointer;
  transition: transform 0.2s ease;
  color: #1296db;
}

.add-task-btn:active {
  transform: scale(0.9);
}

.task-list {
  max-height: 60vh;
  overflow-y: auto;
}

.task-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  transition: background-color 0.2s ease;
}

.task-item:last-child {
  border-bottom: none;
}

.task-item:active {
  background-color: rgba(0, 0, 0, 0.02);
  border-radius: 16rpx;
}

.task-content {
  display: flex;
  align-items: center;
  flex: 1;
}

.task-emoji {
  font-size: 36rpx;
  margin-right: 20rpx;
  width: 60rpx;
  text-align: center;
}

.task-text {
  font-size: 32rpx;
  color: #374151;
  line-height: 1.4;
  flex: 1;
}

.task-check {
  margin-left: 20rpx;
}

.check-completed {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #10b981, #059669);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 8rpx rgba(16, 185, 129, 0.3);
}

.check-mark {
  color: white;
  font-size: 28rpx;
  font-weight: 600;
}

.check-uncompleted {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  border: 3rpx solid #e5e7eb;
  background: white;
  transition: border-color 0.2s ease;
}

.check-uncompleted:active {
  border-color: #10b981;
}

/* 设置弹窗样式 */
.settings-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1001;
  background-color: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(8rpx);
  display: flex;
  align-items: center;
  justify-content: center;
}

.settings-modal {
  width: 90%;
  max-width: 600rpx;
  max-height: 80vh;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 32rpx;
  padding: 40rpx;
  box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.15);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
}

.settings-header-left {
}

.settings-icon {
  font-size: 48rpx;
  margin-right: 16rpx;
}

.settings-title {
  font-size: 48rpx;
  font-weight: 600;
  color: #1f2937;
}

.settings-list {
}

.settings-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  transition: background-color 0.2s ease;
}

.settings-item:last-child {
  border-bottom: none;
}

.settings-item:active {
  background-color: rgba(0, 0, 0, 0.02);
  border-radius: 16rpx;
}

.settings-content {
  display: flex;
  align-items: center;
  flex: 1;
}

.settings-emoji {
  font-size: 36rpx;
  margin-right: 20rpx;
  width: 60rpx;
  text-align: center;
}

.settings-text {
  font-size: 32rpx;
  color: #374151;
  line-height: 1.4;
  flex: 1;
}

.settings-arrow {
  font-size: 32rpx;
  color: #9ca3af;
  margin-left: 20rpx;
}

/* 动画效果 */
.rabbit-container {
  animation: gentleFloat 4s ease-in-out infinite;
}

.animated-rabbit {
  animation: gentleBreathe 3s ease-in-out infinite;
  transform-origin: center center;
}

@keyframes gentleFloat {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-15rpx);
  }
}

@keyframes gentleBreathe {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

/* 创建伙伴弹窗样式 */
.partner-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1002;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(10rpx);
  display: flex;
  align-items: center;
  justify-content: center;
}

.partner-modal {
  width: 85%;
  max-width: 500rpx;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20rpx);
  border-radius: 40rpx;
  padding: 60rpx 40rpx;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.2);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(50rpx);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.partner-header {
  text-align: center;
  margin-bottom: 50rpx;
}

.partner-title {
  font-size: 40rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 16rpx;
  line-height: 1.3;
}

.partner-subtitle {
  font-size: 28rpx;
  color: #6b7280;
  line-height: 1.4;
}

.partner-input-container {
  margin-bottom: 50rpx;
}

.partner-input {
  width: 100%;
  height: 100rpx;
  background: rgba(248, 250, 252, 0.8);
  border: 2rpx solid #e5e7eb;
  border-radius: 20rpx;
  padding: 0 30rpx;
  font-size: 32rpx;
  color: #374151;
  box-sizing: border-box;
  transition: all 0.2s ease;
}

.partner-input:focus {
  border-color: #3b82f6;
  background: rgba(255, 255, 255, 0.9);
  box-shadow: 0 0 0 6rpx rgba(59, 130, 246, 0.1);
}

.partner-input::placeholder {
  color: #9ca3af;
}

.partner-buttons {
  display: flex;
  gap: 20rpx;
}

.partner-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: 500;
  transition: all 0.2s ease;
  cursor: pointer;
}

.partner-btn.cancel-btn {
  background: rgba(243, 244, 246, 0.8);
  color: #6b7280;
  border: 1rpx solid #e5e7eb;
}

.partner-btn.cancel-btn:active {
  transform: scale(0.98);
  background: rgba(229, 231, 235, 0.8);
}

.partner-btn.confirm-btn {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  box-shadow: 0 4rpx 12rpx rgba(59, 130, 246, 0.3);
  background: #333;
}

.partner-btn.confirm-btn:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(59, 130, 246, 0.4);
}

/* 圆形启动按钮样式 */
.start-game-btn {
  position: fixed;
  bottom: 200rpx;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
  animation: pulseGlow 2s ease-in-out infinite;
  border-radius: 50%;
}

.start-btn-circle {
  width: 200rpx;
  height: 200rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20rpx);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  cursor: pointer;
}

.start-btn-circle:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.2);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.3);
}

.start-btn-text {
  color: white;
  font-size: 32rpx;
  font-weight: 600;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

@keyframes pulseGlow {
  0%, 100% {
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.2), 0 0 0 0 rgba(255, 255, 255, 0.4);
  }
  50% {
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.2), 0 0 0 20rpx rgba(255, 255, 255, 0);
  }
}

/* Task icon with rotating ring */
.task-icon-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.task-activity-ring {
  position: absolute;
  width: 80rpx;
  height: 80rpx;
  top: 43%;
  left: 44%;
  transform: translate(-50%, -50%);

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 6rpx solid rgba(33, 150, 243, 0.2);
    border-radius: 50%;
  }

  .task-ring-progress {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 6rpx solid transparent;
    border-top-color: #2196f3;
    border-right-color: #2196f3;
    border-bottom-color: #2196f3;
    border-radius: 50%;
    transform-origin: center;
    animation: task-ring-rotate 3s ease-in-out infinite;
  }
}

@keyframes task-ring-rotate {
  0% { 
    transform: rotate(0deg);
    border-top-color: #2196f3;
    border-right-color: transparent;
    border-bottom-color: transparent;
  }
  50% { 
    transform: rotate(180deg);
    border-top-color: #2196f3;
    border-right-color: #2196f3;
    border-bottom-color: transparent;
  }
  100% { 
    transform: rotate(360deg);
    border-top-color: #2196f3;
    border-right-color: #2196f3;
    border-bottom-color: #2196f3;
  }
}
</style>