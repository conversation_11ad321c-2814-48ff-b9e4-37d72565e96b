package com.kibi.controller;

import com.kibi.entity.CreateChat;
import com.kibi.entity.PartnerChat;
import com.kibi.service.CreateChatService;
import com.kibi.service.PartnerChatService;
import com.kibi.utils.JWTUtils;
import com.kibi.utils.R;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;

@RestController
@RequestMapping("/initPartnerChat")
public class PartnerChatController {

    @Autowired
    private PartnerChatService partnerChatService;

    @Autowired
    private JWTUtils jwtUtils;

    @GetMapping
    public R initDiary(@RequestHeader("Authorization") String authHeader) {
        // 验证token
        String token = jwtUtils.extractTokenFromHeader(authHeader);
        if (token == null || !jwtUtils.validateToken(token)) {
            return R.error("用户未登录或token无效");
        }

        // 获取当前用户ID
        Long currentUserId = jwtUtils.getUserIdFromToken(token);
        PartnerChat ById = partnerChatService.getById(currentUserId.intValue());
        if (ById != null) return R.success(ById);

        PartnerChat partnerChat = new PartnerChat();
        partnerChat.setUserId(currentUserId.intValue());
        partnerChat.setDiaryDate(LocalDate.now());
        boolean save = partnerChatService.save(partnerChat);
        if (save) return R.success(partnerChat);
        return R.error("服务器错误");
    }
}
