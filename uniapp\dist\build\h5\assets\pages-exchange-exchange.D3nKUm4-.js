import{d as e,af as t,A as a,I as l,c as s,w as o,ai as n,i,o as u,a as c,O as r,j as m,e as d,S as p,f as h,F as v,r as f,p as g,b as _,t as k,a4 as y,av as C,ak as w,aw as S,an as D,al as N,am as I,ax as b,ay as x,az as T,as as A,at as O,au as L}from"./index-C6kXbWaK.js";import{_ as V}from"./back.DIErNce1.js";import{_ as P}from"./_plugin-vue_export-helper.BCo6x5W8.js";const F="/static/ai/close.png",j={posts:{all:[{id:1,title:"今天心情特别好，分享一下我的小确幸",content:"早上起来看到阳光透过窗帘洒进房间，突然觉得生活真美好。昨天晚上和朋友聊天到很晚，聊了很多关于未来的规划，感觉充满了希望。今天准备去公园走走，享受这难得的好天气。",authorName:"阳光小雨",authorAvatar:"https://picsum.photos/100/100?random=1",categoryName:"心情感悟",createTime:new Date(Date.now()-72e5).toISOString(),likeCount:23,commentCount:8,isLiked:!1,images:["https://picsum.photos/400/300?random=101","https://picsum.photos/400/300?random=102"]},{id:2,title:"学习Vue3的一些心得体会",content:"最近在学习Vue3，发现Composition API真的很强大。相比Vue2的Options API，代码组织更加灵活，逻辑复用也更方便。特别是ref和reactive的使用，让状态管理变得更加直观。分享一些学习资源给大家。",authorName:"代码小白",authorAvatar:"https://picsum.photos/100/100?random=2",categoryName:"学习交流",createTime:new Date(Date.now()-144e5).toISOString(),likeCount:45,commentCount:12,isLiked:!0,images:["https://picsum.photos/400/300?random=103"]},{id:3,title:"求助：如何平衡工作和生活？",content:"最近工作压力很大，经常加班到很晚，感觉没有时间陪家人和朋友。想问问大家是怎么平衡工作和生活的？有什么好的建议吗？特别是如何在忙碌的工作中保持身心健康。",authorName:"忙碌的蜜蜂",authorAvatar:"https://picsum.photos/100/100?random=3",categoryName:"问题求助",createTime:new Date(Date.now()-216e5).toISOString(),likeCount:18,commentCount:25,isLiked:!1,images:[]},{id:4,title:"分享一个超好用的时间管理方法",content:"最近发现了番茄工作法，真的太好用了！25分钟专注工作，5分钟休息，每4个番茄后休息15-30分钟。这样不仅提高了工作效率，还能保持精力充沛。推荐给经常拖延的朋友们试试。",authorName:"效率达人",authorAvatar:"https://picsum.photos/100/100?random=4",categoryName:"技能分享",createTime:new Date(Date.now()-288e5).toISOString(),likeCount:67,commentCount:15,isLiked:!0,images:["https://picsum.photos/400/300?random=104","https://picsum.photos/400/300?random=105","https://picsum.photos/400/300?random=106"]},{id:5,title:"今天做了一顿丰盛的晚餐",content:"周末在家尝试做了红烧肉、蒸蛋羹和青菜，虽然卖相一般，但味道还不错。做饭真的是一件很治愈的事情，看着食材在手中变成美味的菜肴，很有成就感。下次想挑战一下更复杂的菜。",authorName:"厨房新手",authorAvatar:"https://picsum.photos/100/100?random=5",categoryName:"日常分享",createTime:new Date(Date.now()-432e5).toISOString(),likeCount:34,commentCount:9,isLiked:!1,images:["https://picsum.photos/400/300?random=107","https://picsum.photos/400/300?random=108","https://picsum.photos/400/300?random=109","https://picsum.photos/400/300?random=110"]}],daily:[{id:6,title:"早起看日出的美好体验",content:"今天5点半起床去看日出，虽然很困但是看到太阳慢慢升起的那一刻，觉得一切都值得了。清晨的空气特别清新，鸟儿在枝头歌唱，整个世界都显得那么宁静美好。",authorName:"早起鸟儿",authorAvatar:"https://picsum.photos/100/100?random=6",categoryName:"日常分享",createTime:new Date(Date.now()-36e5).toISOString(),likeCount:28,commentCount:6,isLiked:!1,images:["https://picsum.photos/400/300?random=111"]},{id:7,title:"和朋友一起逛街买到心仪的衣服",content:"今天和闺蜜一起去商场逛街，终于买到了心仪已久的那件外套。试穿的时候朋友说特别适合我，心情瞬间变好了。逛街真的是女生最好的解压方式之一。",authorName:"购物小达人",authorAvatar:"https://picsum.photos/100/100?random=7",categoryName:"日常分享",createTime:new Date(Date.now()-108e5).toISOString(),likeCount:19,commentCount:4,isLiked:!0,images:["https://picsum.photos/400/300?random=112","https://picsum.photos/400/300?random=113"]}],mood:[{id:8,title:"突然想起小时候的那些美好时光",content:"今天路过小学门口，看到孩子们放学的场景，突然想起了自己的童年。那时候没有手机，没有网络，但是有最纯真的快乐。和小伙伴一起玩游戏，一起做作业，一起分享零食，那些简单的快乐现在想起来还是那么温暖。",authorName:"怀旧少年",authorAvatar:"https://picsum.photos/100/100?random=8",categoryName:"心情感悟",createTime:new Date(Date.now()-72e5).toISOString(),likeCount:42,commentCount:16,isLiked:!1,images:[]},{id:9,title:"失恋后的一些感悟",content:"分手已经一个月了，从最初的痛苦到现在的平静，我学会了很多。爱情不是生活的全部，我们要学会爱自己，学会独立。虽然还是会想念，但我知道这是成长必经的路。感谢那段美好的回忆，也感谢现在更强大的自己。",authorName:"重新出发",authorAvatar:"https://picsum.photos/100/100?random=9",categoryName:"心情感悟",createTime:new Date(Date.now()-18e6).toISOString(),likeCount:56,commentCount:23,isLiked:!0,images:["https://picsum.photos/400/300?random=114"]}],study:[{id:10,title:"考研复习的一些经验分享",content:"考研已经进入冲刺阶段，分享一些复习经验。首先要制定详细的复习计划，其次要保持良好的作息，最重要的是要坚持。虽然过程很辛苦，但想到未来的目标就有了动力。加油，所有考研的朋友们！",authorName:"考研战士",authorAvatar:"https://picsum.photos/100/100?random=10",categoryName:"学习交流",createTime:new Date(Date.now()-36e5).toISOString(),likeCount:73,commentCount:28,isLiked:!0,images:["https://picsum.photos/400/300?random=115","https://picsum.photos/400/300?random=116"]}],skill:[{id:11,title:"Photoshop修图技巧分享",content:"最近学了一些PS修图技巧，特别是人像修图。分享几个实用的技巧：1.用修复画笔工具去除瑕疵 2.用曲线调整明暗对比 3.用色彩平衡调整肤色。希望对喜欢摄影的朋友有帮助。",authorName:"修图师小王",authorAvatar:"https://picsum.photos/100/100?random=11",categoryName:"技能分享",createTime:new Date(Date.now()-108e5).toISOString(),likeCount:89,commentCount:31,isLiked:!1,images:["https://picsum.photos/400/300?random=117","https://picsum.photos/400/300?random=118","https://picsum.photos/400/300?random=119"]}],help:[{id:12,title:"求推荐好用的学习APP",content:"最近想提升自己，求推荐一些好用的学习APP。主要想学英语和编程，希望有经验的朋友能推荐一些靠谱的应用。最好是免费或者性价比高的，谢谢大家！",authorName:"学习小白",authorAvatar:"https://picsum.photos/100/100?random=12",categoryName:"问题求助",createTime:new Date(Date.now()-144e5).toISOString(),likeCount:35,commentCount:42,isLiked:!1,images:[]}],other:[{id:13,title:"推荐一部最近看的好电影",content:"最近看了《你好，李焕英》，真的被感动哭了。电影讲述了女儿穿越回过去想让妈妈过得更好的故事，但最后发现妈妈最大的愿望就是女儿健康快乐。珍惜身边的人，特别是我们的父母。",authorName:"电影爱好者",authorAvatar:"https://picsum.photos/100/100?random=13",categoryName:"其他",createTime:new Date(Date.now()-216e5).toISOString(),likeCount:64,commentCount:18,isLiked:!0,images:["https://picsum.photos/400/300?random=120"]}]},comments:{1:[{id:101,content:"看到你的分享也感觉心情变好了，正能量满满！",authorName:"快乐小天使",authorAvatar:"https://picsum.photos/100/100?random=21",createTime:new Date(Date.now()-36e5).toISOString()},{id:102,content:"是啊，有时候简单的小事就能带来很大的快乐",authorName:"简单生活",authorAvatar:"https://picsum.photos/100/100?random=22",createTime:new Date(Date.now()-18e5).toISOString()}],2:[{id:103,content:"Vue3确实很强大，我也在学习中，一起加油！",authorName:"前端小菜鸟",authorAvatar:"https://picsum.photos/100/100?random=23",createTime:new Date(Date.now()-72e5).toISOString()}]}},M=P(e({__name:"exchange",setup(e){const P=t(),M=a([{name:"全部",key:"all"},{name:"日常分享",key:"daily"},{name:"心情感悟",key:"mood"},{name:"学习交流",key:"study"},{name:"技能分享",key:"skill"},{name:"问题求助",key:"help"},{name:"其他",key:"other"}]),U=a(0),$=a([]),z=a(!1),E=a(!1),R=a(!1),q=a(!1),B=a(1),G=a(!1),H=a({categoryIndex:0,title:"",content:"",images:[]}),J=a(!1),K=a(null),Q=a([]),W=a(""),X=async()=>{if(!z.value&&!E.value){1===B.value?z.value=!0:E.value=!0;try{const e=M.value[U.value],t=j.posts[e.key]||[],a=10*(B.value-1),l=a+10,s=t.slice(a,l);1===B.value?$.value=s:$.value.push(...s),s.length<10&&(q.value=!0),await new Promise(e=>setTimeout(e,500))}catch(e){console.error("加载帖子失败:",e),n({title:"加载失败",icon:"none"})}finally{z.value=!1,E.value=!1,R.value=!1}}},Y=()=>{q.value||E.value||(B.value++,X())},Z=()=>{R.value=!0,B.value=1,q.value=!1,X()},ee=()=>{P.profile?G.value=!0:n({title:"请先登录",icon:"none"})},te=()=>{G.value=!1,H.value={categoryIndex:0,title:"",content:"",images:[]}},ae=e=>{H.value.categoryIndex=e.detail.value},le=()=>{D({count:9-H.value.images.length,sizeType:["compressed"],sourceType:["album","camera"],success:e=>{H.value.images.push(...e.tempFilePaths)}})},se=async()=>{var e,t;if(H.value.title.trim())if(H.value.content.trim())try{N({title:"发布中...",mask:!0}),await new Promise(e=>setTimeout(e,1e3));const a=M.value[H.value.categoryIndex],l={id:Date.now(),title:H.value.title,content:H.value.content,authorName:(null==(e=P.profile)?void 0:e.nickname)||"匿名用户",authorAvatar:(null==(t=P.profile)?void 0:t.avatar)||"https://picsum.photos/100/100?random=999",categoryName:a.name,createTime:(new Date).toISOString(),likeCount:0,commentCount:0,isLiked:!1,images:H.value.images};$.value.unshift(l),I(),n({title:"发布成功",icon:"success"}),te()}catch(a){I(),n({title:"发布失败",icon:"none"})}else n({title:"请输入内容",icon:"none"});else n({title:"请输入标题",icon:"none"})},oe=async e=>{K.value=e,J.value=!0;try{Q.value=j.comments[e.id]||[]}catch(t){console.error("加载评论失败:",t)}},ne=()=>{J.value=!1,K.value=null,Q.value=[],W.value=""},ie=async()=>{var e,t;if(W.value.trim())try{await new Promise(e=>setTimeout(e,500)),Q.value.unshift({id:Date.now(),content:W.value,authorName:(null==(e=P.profile)?void 0:e.nickname)||"匿名用户",authorAvatar:(null==(t=P.profile)?void 0:t.avatar)||"https://picsum.photos/100/100?random=999",createTime:(new Date).toISOString()}),K.value&&K.value.commentCount++,W.value="",n({title:"评论成功",icon:"success"})}catch(a){n({title:"评论失败",icon:"none"})}else n({title:"请输入评论内容",icon:"none"})},ue=(e,t)=>{T({urls:e,current:t})},ce=e=>{const t=new Date(e),a=(new Date).getTime()-t.getTime(),l=6e4,s=36e5,o=24*s;return a<l?"刚刚":a<s?`${Math.floor(a/l)}分钟前`:a<o?`${Math.floor(a/s)}小时前`:a<7*o?`${Math.floor(a/o)}天前`:t.toLocaleDateString()},re=e=>{console.log("图片加载失败:",e)},me=()=>{1==A().length?O({url:"/pages/ai/ai"}):L({delta:1})};return l(()=>{X()}),(e,t)=>{const a=m,l=i,D=_,N=p,I=C,T=w,A=S;return u(),s(l,{class:"exchange-container"},{default:o(()=>[c(l,{class:"header"},{default:o(()=>[c(l,{class:"header-left",onClick:me},{default:o(()=>[c(a,{src:V,class:"back-icon"})]),_:1}),c(l,{class:"header-title"},{default:o(()=>[d("社区交流")]),_:1}),c(l,{class:"header-right",onClick:ee},{default:o(()=>[c(a,{src:"/static/ai/pulish.png",class:"edit-icon"})]),_:1})]),_:1}),c(l,{class:"category-tabs"},{default:o(()=>[c(N,{"scroll-x":"true",class:"tabs-scroll"},{default:o(()=>[c(l,{class:"tabs-container"},{default:o(()=>[(u(!0),h(v,null,f(M.value,(e,t)=>(u(),s(l,{key:t,class:g(["tab-item",{active:U.value===t}]),onClick:e=>(e=>{U.value=e,B.value=1,$.value=[],q.value=!1,X()})(t)},{default:o(()=>[c(D,{class:"tab-text"},{default:o(()=>[d(k(e.name),1)]),_:2},1024)]),_:2},1032,["class","onClick"]))),128))]),_:1})]),_:1})]),_:1}),c(N,{"scroll-y":"true",class:"posts-container",onScrolltolower:Y,"refresher-enabled":"",onRefresherrefresh:Z,"refresher-triggered":R.value},{default:o(()=>[c(l,{class:"posts-list"},{default:o(()=>[(u(!0),h(v,null,f($.value,e=>(u(),s(l,{key:e.id,class:"post-item",onClick:t=>oe(e)},{default:o(()=>[c(l,{class:"post-header"},{default:o(()=>[c(l,{class:"author-info"},{default:o(()=>[c(a,{src:e.authorAvatar,class:"author-avatar"},null,8,["src"]),c(l,{class:"author-details"},{default:o(()=>[c(D,{class:"author-name"},{default:o(()=>[d(k(e.authorName),1)]),_:2},1024),c(D,{class:"post-time"},{default:o(()=>[d(k(ce(e.createTime)),1)]),_:2},1024)]),_:2},1024)]),_:2},1024),c(l,{class:"post-category"},{default:o(()=>[c(D,{class:"category-tag"},{default:o(()=>[d(k(e.categoryName),1)]),_:2},1024)]),_:2},1024)]),_:2},1024),c(l,{class:"post-content"},{default:o(()=>[c(l,{class:"post-title"},{default:o(()=>[d(k(e.title),1)]),_:2},1024),c(D,{class:"post-excerpt"},{default:o(()=>[d(k(e.content),1)]),_:2},1024),e.images&&e.images.length>0?(u(),s(l,{key:0,class:"post-images"},{default:o(()=>[(u(!0),h(v,null,f(e.images.slice(0,3),(t,l)=>(u(),s(a,{key:l,src:t,class:"post-image",mode:"aspectFill",onClick:y(t=>ue(e.images,l),["stop"]),onError:re},null,8,["src","onClick"]))),128)),e.images.length>3?(u(),s(l,{key:0,class:"more-images"},{default:o(()=>[c(D,null,{default:o(()=>[d("+"+k(e.images.length-3),1)]),_:2},1024)]),_:2},1024)):r("",!0)]),_:2},1024)):r("",!0)]),_:2},1024),c(l,{class:"post-actions"},{default:o(()=>[c(l,{class:"action-item",onClick:y(e=>(async()=>{if(P.profile)try{n({title:"点赞+1",icon:"none"})}catch(e){n({title:"操作失败",icon:"none"})}else n({title:"请先登录",icon:"none"})})(),["stop"])},{default:o(()=>[c(D,{class:"action-text"},{default:o(()=>[d("♥ "+k(e.likeCount||0),1)]),_:2},1024)]),_:2},1032,["onClick"]),c(l,{class:"action-item",onClick:y(t=>oe(e),["stop"])},{default:o(()=>[c(D,{class:"action-text"},{default:o(()=>[d("💬 "+k(e.commentCount||0),1)]),_:2},1024)]),_:2},1032,["onClick"]),c(l,{class:"action-item",onClick:y(e=>{b({itemList:["分享到微信","复制链接"],success:e=>{0===e.tapIndex?n({title:"分享功能开发中",icon:"none"}):1===e.tapIndex&&x({data:"https://lintu.tech/pages/exchange/exchange",success:()=>{n({title:"链接已复制",icon:"success"})}})}})},["stop"])},{default:o(()=>[c(a,{src:"/static/ai/share.png",class:"action-icon"}),c(D,{class:"action-text"},{default:o(()=>[d("分享")]),_:1})]),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1032,["onClick"]))),128))]),_:1}),E.value?(u(),s(l,{key:0,class:"loading-more"},{default:o(()=>[c(D,null,{default:o(()=>[d("加载更多...")]),_:1})]),_:1})):r("",!0),q.value&&$.value.length>0?(u(),s(l,{key:1,class:"no-more"},{default:o(()=>[c(D,null,{default:o(()=>[d("没有更多内容了")]),_:1})]),_:1})):r("",!0),z.value||0!==$.value.length?r("",!0):(u(),s(l,{key:2,class:"empty-state"},{default:o(()=>[c(D,{class:"empty-text"},{default:o(()=>[d("还没有帖子，快来发布第一个吧！")]),_:1})]),_:1}))]),_:1},8,["refresher-triggered"]),G.value?(u(),s(l,{key:0,class:"publish-modal-overlay",onClick:te},{default:o(()=>[c(l,{class:"publish-modal",onClick:t[2]||(t[2]=y(()=>{},["stop"]))},{default:o(()=>[c(l,{class:"publish-header"},{default:o(()=>[c(D,{class:"publish-title"},{default:o(()=>[d("发布帖子")]),_:1}),c(l,{class:"publish-close",onClick:te},{default:o(()=>[c(a,{src:F,class:"close-icon"})]),_:1})]),_:1}),c(l,{class:"form-group"},{default:o(()=>[c(D,{class:"form-label"},{default:o(()=>[d("选择分类")]),_:1}),c(I,{range:M.value,"range-key":"name",value:H.value.categoryIndex,onChange:ae},{default:o(()=>[c(l,{class:"picker-input"},{default:o(()=>{var e;return[d(k((null==(e=M.value[H.value.categoryIndex])?void 0:e.name)||"请选择分类"),1)]}),_:1})]),_:1},8,["range","value"])]),_:1}),c(l,{class:"form-group"},{default:o(()=>[c(D,{class:"form-label"},{default:o(()=>[d("标题")]),_:1}),c(T,{modelValue:H.value.title,"onUpdate:modelValue":t[0]||(t[0]=e=>H.value.title=e),placeholder:"请输入标题...",class:"title-input",maxlength:"50"},null,8,["modelValue"])]),_:1}),c(l,{class:"form-group"},{default:o(()=>[c(D,{class:"form-label"},{default:o(()=>[d("内容")]),_:1}),c(A,{modelValue:H.value.content,"onUpdate:modelValue":t[1]||(t[1]=e=>H.value.content=e),placeholder:"分享你的想法...",class:"content-input",maxlength:"500","auto-height":""},null,8,["modelValue"])]),_:1}),c(l,{class:"form-group"},{default:o(()=>[c(D,{class:"form-label"},{default:o(()=>[d("图片 (最多9张)")]),_:1}),c(l,{class:"image-upload"},{default:o(()=>[(u(!0),h(v,null,f(H.value.images,(e,t)=>(u(),s(l,{key:t,class:"upload-item"},{default:o(()=>[c(a,{src:e,class:"upload-image",mode:"aspectFill"},null,8,["src"]),c(l,{class:"delete-btn",onClick:e=>(e=>{H.value.images.splice(e,1)})(t)},{default:o(()=>[c(D,null,{default:o(()=>[d("×")]),_:1})]),_:2},1032,["onClick"])]),_:2},1024))),128)),H.value.images.length<9?(u(),s(l,{key:0,class:"upload-btn",onClick:le},{default:o(()=>[c(D,{class:"upload-text"},{default:o(()=>[d("+")]),_:1})]),_:1})):r("",!0)]),_:1})]),_:1}),c(l,{class:"publish-actions"},{default:o(()=>[c(l,{class:"publish-btn",onClick:se},{default:o(()=>[c(D,{class:"publish-btn-text"},{default:o(()=>[d("发布")]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})):r("",!0),J.value?(u(),s(l,{key:1,class:"detail-modal-overlay",onClick:ne},{default:o(()=>[c(l,{class:"detail-modal",onClick:t[4]||(t[4]=y(()=>{},["stop"]))},{default:o(()=>[c(l,{class:"detail-header"},{default:o(()=>[c(D,{class:"detail-title"},{default:o(()=>[d("帖子详情")]),_:1}),c(l,{class:"detail-close",onClick:ne},{default:o(()=>[c(a,{src:F,class:"close-icon"})]),_:1})]),_:1}),c(N,{"scroll-y":"true",class:"detail-content"},{default:o(()=>[K.value?(u(),s(l,{key:0,class:"post-detail"},{default:o(()=>[c(l,{class:"detail-post-header"},{default:o(()=>[c(a,{src:K.value.authorAvatar,class:"detail-author-avatar"},null,8,["src"]),c(l,{class:"detail-author-info"},{default:o(()=>[c(D,{class:"detail-author-name"},{default:o(()=>[d(k(K.value.authorName),1)]),_:1}),c(D,{class:"detail-post-time"},{default:o(()=>[d(k(ce(K.value.createTime)),1)]),_:1})]),_:1})]),_:1}),c(l,{class:"detail-post-title"},{default:o(()=>[d(k(K.value.title),1)]),_:1}),c(l,{class:"detail-post-content"},{default:o(()=>[c(D,null,{default:o(()=>[d(k(K.value.content),1)]),_:1})]),_:1}),K.value.images&&K.value.images.length>0?(u(),s(l,{key:0,class:"detail-images"},{default:o(()=>[(u(!0),h(v,null,f(K.value.images,(e,t)=>(u(),s(a,{key:t,src:e,class:"detail-image",mode:"aspectFill",onClick:e=>ue(K.value.images,t)},null,8,["src","onClick"]))),128))]),_:1})):r("",!0)]),_:1})):r("",!0),c(l,{class:"comments-section"},{default:o(()=>[c(D,{class:"comments-title"},{default:o(()=>[d("评论 ("+k(Q.value.length)+")",1)]),_:1}),(u(!0),h(v,null,f(Q.value,e=>(u(),s(l,{key:e.id,class:"comment-item"},{default:o(()=>[c(a,{src:e.authorAvatar,class:"comment-avatar"},null,8,["src"]),c(l,{class:"comment-content"},{default:o(()=>[c(l,{class:"comment-author"},{default:o(()=>[d(k(e.authorName),1),c(D,{class:"comment-time"},{default:o(()=>[d(k(ce(e.createTime)),1)]),_:2},1024)]),_:2},1024),c(D,{class:"comment-text"},{default:o(()=>[d(k(e.content),1)]),_:2},1024)]),_:2},1024)]),_:2},1024))),128))]),_:1})]),_:1}),c(l,{class:"comment-input-bar"},{default:o(()=>[c(T,{modelValue:W.value,"onUpdate:modelValue":t[3]||(t[3]=e=>W.value=e),placeholder:"写下你的评论...",class:"comment-input",onConfirm:ie},null,8,["modelValue"]),c(l,{class:"comment-send-btn",onClick:ie},{default:o(()=>[c(D,null,{default:o(()=>[d("发送")]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})):r("",!0)]),_:1})}}}),[["__scopeId","data-v-65b494f8"]]);export{M as default};
