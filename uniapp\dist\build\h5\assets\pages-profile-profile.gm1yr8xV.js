import{ah as e,d as a,af as l,A as t,c as n,w as s,i as c,o as i,a as o,O as u,b as d,e as r,j as f,u as m,t as p,aq as v,a4 as _,ak as k,ar as h,ai as g,as as b,at as w,au as y,al as C,am as O}from"./index-C6kXbWaK.js";import{_ as x}from"./cancel.DNEHwb_h.js";import{_ as N}from"./_plugin-vue_export-helper.BCo6x5W8.js";const F=N(a({__name:"profile",setup(a){const N=l(),F=t({locationOn:!1,planetMusicOn:!1}),V=t(!1),M=t(""),j=t(""),P=t(""),S=t(!1),T=t(""),D=t(!1),U=()=>{g({title:"敬请期待",icon:"none"}),F.value.locationOn=!F.value.locationOn},q=()=>{g({title:"敬请期待",icon:"none"}),F.value.planetMusicOn=!F.value.planetMusicOn},z=()=>{g({title:"敬请期待",icon:"none"})},A=()=>{1==b().length?w({url:"/pages/ai/ai"}):y({delta:1})},I=e=>{switch(M.value=e,e){case"name":P.value="修改昵称",j.value=N.profile.nickname;break;case"friendName":P.value="修改伙伴",j.value=N.profile.partner.nickName;break;case"phone":P.value="修改联系号码",j.value=N.profile.phone}V.value=!0},B=()=>{V.value=!1,M.value="",j.value="",P.value=""},E=async()=>{if(j.value.trim()){C({title:"保存中..."});try{switch(M.value){case"name":const t=await(l=j.value.trim(),e({method:"POST",url:"/user/updateNickname",data:{nickname:l},header:{"content-type":"application/x-www-form-urlencoded"}}));if(200!==t.code)return void g({title:t.msg||"更新失败",icon:"none"});F.value.name=j.value.trim(),N.profile.nickname=j.value.trim(),g({title:"已保存",icon:"success"});break;case"friendName":console.log("...");const n=await(a=>e({method:"POST",url:"/partner/updateNickname",data:{nickname:a},header:{"content-type":"application/x-www-form-urlencoded"}}))(j.value.trim());if(200!==n.code)return void g({title:n.msg||"更新失败",icon:"none"});F.value.friendName=j.value.trim(),N.profile.partner.nickName=j.value.trim(),g({title:"已保存",icon:"success"});break;case"phone":const s=await(a=j.value.trim(),e({method:"POST",url:"/user/updatePhone",data:{phone:a},header:{"content-type":"application/x-www-form-urlencoded"}}));if(200!==s.code)return void g({title:s.msg||"更新失败",icon:"none"});F.value.phone=j.value.trim(),N.profile.mobile=j.value.trim(),g({title:"联系号码更新成功",icon:"success"})}B()}catch(t){console.error("更新失败:",t),g({title:"网络错误，请稍后重试",icon:"none"})}finally{O()}var a,l}else g({title:"请输入内容",icon:"none"})},G=()=>{T.value="",S.value=!0},H=()=>{S.value=!1,D.value=!1,T.value=""},J=()=>{var e,a;const l=null==(a=null==(e=N.profile)?void 0:e.partner)?void 0:a.nickName;T.value.trim()===l?D.value=!0:g({title:"星灵名称输入错误",icon:"none"})},K=()=>{H(),L()},L=async()=>{C({title:"删除中..."});try{const a=await e({});200===a.code?(N.profile.partner=null,g({title:"星灵已删除",icon:"success"})):g({title:a.msg||"删除失败",icon:"none"})}catch(a){console.error("删除星灵失败:",a),g({title:"网络错误，请稍后重试",icon:"none"})}finally{O()}};return(e,a)=>{const l=d,t=c,g=f,b=v,w=k,y=h;return i(),n(t,{class:"profile-container"},{default:s(()=>[o(t,{class:"header"},{default:s(()=>[o(t,{class:"back-btn",onClick:A},{default:s(()=>[o(l,{class:"back-icon"},{default:s(()=>[r("‹")]),_:1})]),_:1}),o(t,{class:"title-container"},{default:s(()=>[o(l,{class:"title"},{default:s(()=>[r("个人资料")]),_:1})]),_:1})]),_:1}),o(t,{class:"profile-content"},{default:s(()=>[o(t,{class:"profile-item"},{default:s(()=>[o(l,{class:"item-label"},{default:s(()=>[r("头像")]),_:1}),o(t,{class:"avatar-container"},{default:s(()=>{var e;return[o(g,{class:"avatar",src:null==(e=m(N).profile)?void 0:e.avatar,mode:"aspectFill"},null,8,["src"])]}),_:1})]),_:1}),o(t,{class:"profile-item clickable",onClick:a[0]||(a[0]=e=>I("name"))},{default:s(()=>[o(l,{class:"item-label"},{default:s(()=>[r("昵称")]),_:1}),o(l,{class:"item-value"},{default:s(()=>[r(p(m(N).profile.nickname),1)]),_:1})]),_:1}),o(t,{class:"profile-item clickable",onClick:a[1]||(a[1]=e=>I("friendName"))},{default:s(()=>[o(l,{class:"item-label"},{default:s(()=>[r("星灵")]),_:1}),o(l,{class:"item-value"},{default:s(()=>{var e;return[r(p((null==(e=m(N).profile)?void 0:e.partner)?m(N).profile.partner.nickName:"暂无"),1)]}),_:1})]),_:1}),o(t,{class:"profile-item clickable",onClick:z},{default:s(()=>[o(l,{class:"item-label"},{default:s(()=>[r("笔记本解锁")]),_:1}),o(t,{class:"unlock-icon"},{default:s(()=>[o(l,{class:"icon"},{default:s(()=>[r("🔓")]),_:1})]),_:1})]),_:1}),o(t,{class:"profile-item"},{default:s(()=>[o(t,{class:"music-content"},{default:s(()=>[o(t,{class:"music-info"},{default:s(()=>[o(t,{class:"item-label"},{default:s(()=>[r("地图展示")]),_:1}),o(t,{class:"item-description"},{default:s(()=>[r("把你的星灵展示在地图上，不会显示个人信息")]),_:1})]),_:1}),o(t,{class:"switch-container"},{default:s(()=>[o(b,{checked:F.value.locationOn,onChange:U,color:"#4CD964"},null,8,["checked"]),o(l,{class:"switch-status"},{default:s(()=>[r(p(F.value.locationOn?"ON":"OFF"),1)]),_:1})]),_:1})]),_:1})]),_:1}),o(t,{class:"profile-item"},{default:s(()=>[o(t,{class:"music-content"},{default:s(()=>[o(t,{class:"music-info"},{default:s(()=>[o(t,{class:"item-label"},{default:s(()=>[r("星球音乐")]),_:1}),o(t,{class:"item-description"},{default:s(()=>{var e,a;return[r("当你不和"+p((null==(e=m(N).profile)?void 0:e.partner)?null==(a=m(N).profile)?void 0:a.partner.nickName:"星灵")+"谈话时，播放舒缓的音乐。",1)]}),_:1})]),_:1}),o(t,{class:"switch-container"},{default:s(()=>[o(b,{checked:F.value.planetMusicOn,onChange:q,color:"#4CD964"},null,8,["checked"]),o(l,{class:"switch-status"},{default:s(()=>[r(p(F.value.planetMusicOn?"ON":"OFF"),1)]),_:1})]),_:1})]),_:1})]),_:1}),o(t,{class:"profile-item clickable",onClick:a[2]||(a[2]=e=>I("phone"))},{default:s(()=>[o(t,{class:"phone-content"},{default:s(()=>[o(t,{class:"item-label"},{default:s(()=>[r("联系号码")]),_:1}),o(t,{class:"item-description"},{default:s(()=>[r("您的电话号码已验证")]),_:1})]),_:1}),o(l,{class:"phone-number"},{default:s(()=>{var e;return[r(p(null==(e=m(N).profile)?void 0:e.phone),1)]}),_:1})]),_:1}),o(t,{onClick:G,style:{width:"100%","text-align":"center","margin-top":"50rpx",color:"#cc3434","font-size":"large","font-weight":"bold"}},{default:s(()=>[r("删除星灵")]),_:1})]),_:1}),V.value?(i(),n(t,{key:0,class:"dialog-overlay",onClick:B},{default:s(()=>[o(t,{class:"dialog-container",onClick:a[4]||(a[4]=_(()=>{},["stop"]))},{default:s(()=>[o(t,{class:"dialog-header"},{default:s(()=>[o(l,{class:"dialog-title"},{default:s(()=>[r(p(P.value),1)]),_:1}),o(t,{class:"cancel-btn",onClick:B},{default:s(()=>[o(g,{class:"cancel-icon",src:x,mode:"aspectFit"})]),_:1})]),_:1}),o(t,{class:"dialog-content"},{default:s(()=>[o(w,{modelValue:j.value,"onUpdate:modelValue":a[3]||(a[3]=e=>j.value=e),class:"dialog-input",placeholder:P.value,type:"phone"===M.value?"number":"text",maxlength:"phone"===M.value?11:20},null,8,["modelValue","placeholder","type","maxlength"])]),_:1}),o(t,{class:"dialog-actions"},{default:s(()=>[o(y,{class:"dialog-btn save-btn",onClick:E},{default:s(()=>[r("保存")]),_:1})]),_:1})]),_:1})]),_:1})):u("",!0),S.value?(i(),n(t,{key:1,class:"dialog-overlay",onClick:H},{default:s(()=>[o(t,{class:"delete-dialog-container",onClick:a[7]||(a[7]=_(()=>{},["stop"]))},{default:s(()=>[o(t,{class:"delete-dialog-header"},{default:s(()=>[o(l,{class:"delete-dialog-title"},{default:s(()=>[r("删除星灵")]),_:1}),o(t,{class:"cancel-btn",onClick:H},{default:s(()=>[o(l,{class:"cancel-text"},{default:s(()=>[r("✕")]),_:1})]),_:1})]),_:1}),D.value?(i(),n(t,{key:1,class:"delete-dialog-content"},{default:s(()=>[o(t,{class:"danger-icon"},{default:s(()=>[o(l,{class:"danger-text"},{default:s(()=>[r("🚨")]),_:1})]),_:1}),o(t,{class:"final-message"},{default:s(()=>[o(l,{class:"final-text"},{default:s(()=>[r("最终确认")]),_:1}),o(l,{class:"final-warning"},{default:s(()=>[r("删除后无法恢复，确定要删除星灵吗？")]),_:1})]),_:1}),o(t,{class:"final-actions"},{default:s(()=>[o(y,{class:"delete-btn cancel-btn-style",onClick:a[6]||(a[6]=e=>D.value=!1)},{default:s(()=>[r("返回")]),_:1}),o(y,{class:"delete-btn danger-btn-style",onClick:K},{default:s(()=>[r("确定删除")]),_:1})]),_:1})]),_:1})):(i(),n(t,{key:0,class:"delete-dialog-content"},{default:s(()=>[o(t,{class:"warning-icon"},{default:s(()=>[o(l,{class:"warning-text"},{default:s(()=>[r("⚠️")]),_:1})]),_:1}),o(t,{class:"delete-message"},{default:s(()=>[o(l,{class:"message-text"},{default:s(()=>[r("请输入星灵名称以确认删除")]),_:1}),o(l,{class:"partner-name"},{default:s(()=>{var e,a;return[r('"'+p(null==(a=null==(e=m(N).profile)?void 0:e.partner)?void 0:a.nickName)+'"',1)]}),_:1})]),_:1}),o(w,{modelValue:T.value,"onUpdate:modelValue":a[5]||(a[5]=e=>T.value=e),class:"delete-input",placeholder:"请输入星灵名称",maxlength:"20"},null,8,["modelValue"]),o(t,{class:"delete-actions"},{default:s(()=>[o(y,{class:"delete-btn cancel-btn-style",onClick:H},{default:s(()=>[r("取消")]),_:1}),o(y,{class:"delete-btn confirm-btn-style",onClick:J},{default:s(()=>[r("确认")]),_:1})]),_:1})]),_:1}))]),_:1})]),_:1})):u("",!0)]),_:1})}}}),[["__scopeId","data-v-eaf49244"]]);export{F as default};
