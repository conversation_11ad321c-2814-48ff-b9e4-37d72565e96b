<script setup lang="ts">
import { ref } from 'vue'
import { useUserStore } from '@/stores'

const userStore = useUserStore()

// 响应式数据
const activeTab = ref('风格定制')
const activeFeature = ref<FeatureType>('推荐')
const selectedColor = ref('#10D07A') // 当前选中的绿色

// 标签页数据
const tabs = ref(['风格定制', '服饰定制'])

// 特征选择标签
const features = ref<FeatureType[]>(['推荐','3D', '动漫', '手办', '写实', '机甲', '宠物'])

// 定义选项类型
interface FeatureOption {
  id: number
  name: string
  preview: string
  description: string
}

// 定义特征选项类型
type FeatureType = '推荐' | '3D' | '动漫' | '手办' | '写实' | '机甲' | '宠物'

// 每个特征的选项数据
const featureOptions = ref<Record<FeatureType, FeatureOption[]>>({
  '推荐': [
    { id: 1, name: '粘土', preview: 'http://m.qpic.cn/psc?/V539rQMC0OrLEH1QHqhY1BLdBc3ErO4h/TmEUgtj9EK6.7V8ajmQrEBqlMsEfDQBNw2my3BBsGA92jVtfESjuMEVuh2ZPTnnpl4loFazEOQd4RcZ2JvQt2xiG2*NDIxr*tre7Fo8dsp4!/b&bo=wgFYAsIBWAICl7M!&rf=viewer_4', description: '可爱卡通黏土风格' },
    { id: 2, name: '民族', preview: 'http://m.qpic.cn/psc?/V539rQMC0OrLEH1QHqhY1BLdBc3ErO4h/TmEUgtj9EK6.7V8ajmQrEBqlMsEfDQBNw2my3BBsGA.iEp5b3WnnNk1fu4.iW1VtV5OWyPYU16FfpSXWokuJ5IhuAvsN0yppWGvaYx*lwFk!/b&bo=DQK8Ag0CvAICl7M!&rf=viewer_4', description: '少数民族风情' },
    { id: 3, name: '吉卜力', preview: 'http://m.qpic.cn/psc?/V539rQMC0OrLEH1QHqhY1BLdBc3ErO4h/TmEUgtj9EK6.7V8ajmQrEBqlMsEfDQBNw2my3BBsGA.NCOFh.MwcX7npzLGUKR92sFytGDy9qeHvROY4Jj0rllupPtTWG3uurkNgk0qtJBw!/b&bo=AAQABgAEAAYCByM!&rf=viewer_4', description: '吉卜力画风' },
    { id: 4, name: '折纸', preview: 'http://m.qpic.cn/psc?/V539rQMC0OrLEH1QHqhY1BLdBc3ErO4h/TmEUgtj9EK6.7V8ajmQrEBqlMsEfDQBNw2my3BBsGA*LkpeK3HlN6NsZMzAyBMsvcMmVNU8cG4*xbsBM32f1tOckZwxeJBv4vE*G2nDTLwU!/b&bo=AAMABAADAAQCByM!&rf=viewer_4', description: '折纸艺术' }
  ],
  '3D': [
    { id: 1, name: '粘土风', preview: '/static/style/clay.gif', description: '可爱卡通黏土风格' },
    { id: 2, name: '民族风', preview: '/static/style/national.gif', description: '少数民族风情' },
    { id: 3, name: '未来风', preview: '/static/ai/3d_future.png', description: '未来主义风格' },
    { id: 4, name: '简约风', preview: '/static/ai/3d_minimal.png', description: '简洁现代设计' }
  ],
  '动漫': [
    { id: 1, name: '日系萌', preview: '/static/ai/anime_kawaii.png', description: '日式可爱风格' },
    { id: 2, name: '少女风', preview: '/static/ai/anime_girl.png', description: '清新少女感' },
    { id: 3, name: '热血风', preview: '/static/ai/anime_hot.png', description: '热血动漫风' },
    { id: 4, name: '治愈系', preview: '/static/ai/anime_heal.png', description: '温暖治愈感' }
  ],
  '手办': [
    { id: 1, name: '精致款', preview: '/static/ai/figure_deluxe.png', description: '精工细作款式' },
    { id: 2, name: 'Q版款', preview: '/static/ai/figure_q.png', description: 'Q萌可爱造型' },
    { id: 3, name: '限定款', preview: '/static/ai/figure_limited.png', description: '限量珍藏版' },
    { id: 4, name: '经典款', preview: '/static/ai/figure_classic.png', description: '经典传统造型' }
  ],
  '写实': [
    { id: 1, name: '自然系', preview: '/static/ai/real_nature.png', description: '自然真实感' },
    { id: 2, name: '优雅系', preview: '/static/ai/real_elegant.png', description: '优雅知性风' },
    { id: 3, name: '活力系', preview: '/static/ai/real_active.png', description: '青春活力感' },
    { id: 4, name: '成熟系', preview: '/static/ai/real_mature.png', description: '成熟稳重风' }
  ],
  '机甲': [
    { id: 1, name: '战斗型', preview: '/static/ai/mecha_battle.png', description: '重装战斗机甲' },
    { id: 2, name: '敏捷型', preview: '/static/ai/mecha_speed.png', description: '轻型高速机甲' },
    { id: 3, name: '防御型', preview: '/static/ai/mecha_defense.png', description: '重甲防护型' },
    { id: 4, name: '辅助型', preview: '/static/ai/mecha_support.png', description: '多功能辅助型' }
  ],
  '宠物': [
    { id: 1, name: '猫咪', preview: '/static/ai/pet_cat.png', description: '可爱小猫咪' },
    { id: 2, name: '小狗', preview: '/static/ai/pet_dog.png', description: '忠诚小伙伴' },
    { id: 3, name: '兔子', preview: '/static/ai/pet_rabbit.png', description: '软萌小兔子' },
    { id: 4, name: '鸟类', preview: '/static/ai/pet_bird.png', description: '聪明小鸟儿' }
  ]
})

// 当前选中的选项
const selectedOption = ref<FeatureOption | null>(null)

// 颜色选项
const colors = ref([
  '#FF9B7A', // 橙色
  '#10D07A', // 绿色(选中)
  '#4FB3F0', // 蓝色
  '#F5D547', // 黄色
  '#3B9AE6', // 深蓝色
  '#B8C5D1', // 灰色
  '#F5A3C7', // 粉色
  '#B084E6', // 紫色
  '#7BC96E'  // 浅绿色
])

// 角色数据
const characterData = ref({
  name: 'kiki',
  color: '#10D07A',
  hair: 'curly',
  cheeks: 'rosy',
  eyes: 'round'
})

// 方法
const switchTab = (tab: string) => {
  activeTab.value = tab
}

const selectFeature = (feature: FeatureType) => {
  activeFeature.value = feature
  selectedOption.value = null // 切换特征时重置选择
}

const selectColor = (color: string) => {
  selectedColor.value = color
  characterData.value.color = color
}

const selectOption = (option: any) => {
  selectedOption.value = option
}

const goBack = () => {
  uni.navigateBack()
}
</script>

<template>
  <view class="style-container">
    <!-- 顶部导航栏 -->
    <view class="header">
      <view class="back-btn" @click="goBack">
        <text class="back-icon">‹</text>
      </view>
      <view class="tabs">
        <view 
          v-for="tab in tabs" 
          :key="tab"
          class="tab"
          :class="{ active: activeTab === tab }"
          @click="switchTab(tab)"
        >
          <text class="tab-text">{{ tab }}</text>
        </view>
      </view>
    </view>

    <!-- Central AI Character - 微动 -->
    <view style="display: flex;justify-content: center;align-items: center; z-index: 1;margin-top: 20rpx;">
      <view class="rabbit-container">
        <image src="/static/test1.png" class="animated-rabbit" style="width: 400rpx; height: 600rpx;" mode="aspectFit" />
      </view>
    </view>

    <!-- 特征选择区域 -->
    <view class="features-section">
      <view class="feature-tabs">
        <view 
          v-for="feature in features" 
          :key="feature"
          class="feature-tab"
          :class="{ active: activeFeature === feature }"
          @click="selectFeature(feature)"
        >
          <text class="feature-text">{{ feature }}</text>
        </view>
      </view>

      <!-- 特征选项网格 -->
      <view class="options-grid">
        <view 
          v-for="option in featureOptions[activeFeature]" 
          :key="option.id"
          class="option-item"
          :class="{ selected: selectedOption?.id === option.id }"
          @click="selectOption(option)"
        >
          <view class="option-preview">
            <image :src="option.preview" class="preview-image" mode="widthFix" />
          </view>
          <view class="option-info">
            <!-- <text class="option-name">{{ option.name }}</text> -->
            <text class="option-description">{{ option.description }}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
.style-container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-top: env(safe-area-inset-top);
}

.header {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  padding: 20rpx 20px;

  .back-btn {
    position: absolute;
    left: 20px;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;

    .back-icon {
      font-size: 50px;
      color: #333;
      font-weight: bold;
    }
  }

  .tabs {
    display: flex;
    gap: 8px;
    background-color: #e5e5e5;
    border-radius: 10px;
    margin-top: 10px;
    .tab {
      padding: 8px 20px;
      border-radius: 10px;
      transition: all 0.2s;

      &.active {
        background-color: white;
      }

      .tab-text {
        font-size: 16px;
        color: #333;
        font-weight: 500;
      }
    }
  }
}

.features-section {
  background-color: white;
  border-radius: 20px 20px 0 0;
  padding: 20px;
  margin-top: 20rpx;

  .feature-tabs {
    display: flex;
    gap: 20px;
    margin: 0 0 15px 10px;
    overflow-x: auto;

    .feature-tab {
      white-space: nowrap;
      cursor: pointer;

      .feature-text {
        font-size: 16px;
        color: #999;
        font-weight: 500;
        transition: color 0.2s;
      }

      &.active .feature-text {
        color: #333;
        font-weight: 600;
      }
    }
  }

  .options-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;

    .option-item {
      background-color: #fff;
      border-radius: 15px;
      cursor: pointer;
      transition: all 0.3s ease;
      border: 2px solid transparent;

      &.selected {
        border-color: #10D07A;
        background-color: #f0fff4;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(16, 208, 122, 0.2);
      }

      &:active {
        transform: scale(0.98);
      }

      .option-preview {
        width: 100%;
        border-radius: 10px;
        margin-bottom: 5px;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;

        .preview-image {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .option-info {
        text-align: center;

        .option-name {
          display: block;
          font-size: 16px;
          font-weight: 600;
          color: #333;
          margin-bottom: 4px;
        }

        .option-description {
          display: block;
          font-size: 12px;
          color: #666;
          line-height: 1.3;
          margin-bottom: 10px;
        }
      }
    }
  }
}

/* 动画效果 */
.rabbit-container {
  animation: gentleFloat 4s ease-in-out infinite;
}

.animated-rabbit {
  animation: gentleBreathe 3s ease-in-out infinite;
  transform-origin: center center;
}

@keyframes gentleFloat {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-15rpx);
  }
}

@keyframes gentleBreathe {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}
</style>