<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.role.mapper.RoleMapper">
    
    <resultMap type="Role" id="RoleResult">
        <result property="userId"    column="user_id"    />
        <result property="traits"    column="traits"    />
        <result property="description"    column="description"    />
        <result property="spontaneous"    column="spontaneous"    />
        <result property="collaborative"    column="collaborative"    />
        <result property="realist"    column="realist"    />
        <result property="logical"    column="logical"    />
        <result property="analytical"    column="analytical"    />
        <result property="introvert"    column="introvert"    />
    </resultMap>

    <sql id="selectRoleVo">
        select user_id, traits, description, spontaneous, collaborative, realist, logical, analytical, introvert from tb_role
    </sql>

    <select id="selectRoleList" parameterType="Role" resultMap="RoleResult">
        <include refid="selectRoleVo"/>
        <where>  
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="traits != null  and traits != ''"> and traits = #{traits}</if>
            <if test="description != null  and description != ''"> and description = #{description}</if>
            <if test="spontaneous != null "> and spontaneous = #{spontaneous}</if>
            <if test="collaborative != null "> and collaborative = #{collaborative}</if>
            <if test="realist != null "> and realist = #{realist}</if>
            <if test="logical != null "> and logical = #{logical}</if>
            <if test="analytical != null "> and analytical = #{analytical}</if>
            <if test="introvert != null "> and introvert = #{introvert}</if>
        </where>
    </select>
    
    <select id="selectRoleByUserId" parameterType="Long" resultMap="RoleResult">
        <include refid="selectRoleVo"/>
        where user_id = #{userId}
    </select>

    <insert id="insertRole" parameterType="Role">
        insert into tb_role
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="traits != null">traits,</if>
            <if test="description != null">description,</if>
            <if test="spontaneous != null">spontaneous,</if>
            <if test="collaborative != null">collaborative,</if>
            <if test="realist != null">realist,</if>
            <if test="logical != null">logical,</if>
            <if test="analytical != null">analytical,</if>
            <if test="introvert != null">introvert,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="traits != null">#{traits},</if>
            <if test="description != null">#{description},</if>
            <if test="spontaneous != null">#{spontaneous},</if>
            <if test="collaborative != null">#{collaborative},</if>
            <if test="realist != null">#{realist},</if>
            <if test="logical != null">#{logical},</if>
            <if test="analytical != null">#{analytical},</if>
            <if test="introvert != null">#{introvert},</if>
         </trim>
    </insert>

    <update id="updateRole" parameterType="Role">
        update tb_role
        <trim prefix="SET" suffixOverrides=",">
            <if test="traits != null">traits = #{traits},</if>
            <if test="description != null">description = #{description},</if>
            <if test="spontaneous != null">spontaneous = #{spontaneous},</if>
            <if test="collaborative != null">collaborative = #{collaborative},</if>
            <if test="realist != null">realist = #{realist},</if>
            <if test="logical != null">logical = #{logical},</if>
            <if test="analytical != null">analytical = #{analytical},</if>
            <if test="introvert != null">introvert = #{introvert},</if>
        </trim>
        where user_id = #{userId}
    </update>

    <delete id="deleteRoleByUserId" parameterType="Long">
        delete from tb_role where user_id = #{userId}
    </delete>

    <delete id="deleteRoleByUserIds" parameterType="String">
        delete from tb_role where user_id in 
        <foreach item="userId" collection="array" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </delete>
</mapper>