package com.ruoyi.role.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 角色管理对象 tb_role
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
public class Role extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 特质 */
    @Excel(name = "特质")
    private String traits;

    /** 描述 */
    @Excel(name = "描述")
    private String description;

    /** 随性 */
    @Excel(name = "随性")
    private Long spontaneous;

    /** 协作 */
    @Excel(name = "协作")
    private Long collaborative;

    /** 务实 */
    @Excel(name = "务实")
    private Long realist;

    /** 逻辑 */
    @Excel(name = "逻辑")
    private Long logical;

    /** 分析 */
    @Excel(name = "分析")
    private Long analytical;

    /** 内向 */
    @Excel(name = "内向")
    private Long introvert;

    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }

    public void setTraits(String traits) 
    {
        this.traits = traits;
    }

    public String getTraits() 
    {
        return traits;
    }

    public void setDescription(String description) 
    {
        this.description = description;
    }

    public String getDescription() 
    {
        return description;
    }

    public void setSpontaneous(Long spontaneous) 
    {
        this.spontaneous = spontaneous;
    }

    public Long getSpontaneous() 
    {
        return spontaneous;
    }

    public void setCollaborative(Long collaborative) 
    {
        this.collaborative = collaborative;
    }

    public Long getCollaborative() 
    {
        return collaborative;
    }

    public void setRealist(Long realist) 
    {
        this.realist = realist;
    }

    public Long getRealist() 
    {
        return realist;
    }

    public void setLogical(Long logical) 
    {
        this.logical = logical;
    }

    public Long getLogical() 
    {
        return logical;
    }

    public void setAnalytical(Long analytical) 
    {
        this.analytical = analytical;
    }

    public Long getAnalytical() 
    {
        return analytical;
    }

    public void setIntrovert(Long introvert) 
    {
        this.introvert = introvert;
    }

    public Long getIntrovert() 
    {
        return introvert;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("userId", getUserId())
            .append("traits", getTraits())
            .append("description", getDescription())
            .append("spontaneous", getSpontaneous())
            .append("collaborative", getCollaborative())
            .append("realist", getRealist())
            .append("logical", getLogical())
            .append("analytical", getAnalytical())
            .append("introvert", getIntrovert())
            .toString();
    }
}
