import{d as e,af as i,A as a,c as t,w as s,i as n,o as r,a as c,b as p,e as l,f as d,F as o,r as m,p as u,t as v,j as f,au as _}from"./index-C6kXbWaK.js";import{_ as w}from"./test1.CfGLGxmt.js";import{_ as g}from"./_plugin-vue_export-helper.BCo6x5W8.js";const B=g(e({__name:"style",setup(e){i();const g=a("风格定制"),B=a("推荐");a("#10D07A");const E=a(["风格定制","服饰定制"]),A=a(["推荐","3D","动漫","手办","写实","机甲","宠物"]),h=a({"推荐":[{id:1,name:"粘土",preview:"http://m.qpic.cn/psc?/V539rQMC0OrLEH1QHqhY1BLdBc3ErO4h/TmEUgtj9EK6.7V8ajmQrEBqlMsEfDQBNw2my3BBsGA92jVtfESjuMEVuh2ZPTnnpl4loFazEOQd4RcZ2JvQt2xiG2*NDIxr*tre7Fo8dsp4!/b&bo=wgFYAsIBWAICl7M!&rf=viewer_4",description:"可爱卡通黏土风格"},{id:2,name:"民族",preview:"http://m.qpic.cn/psc?/V539rQMC0OrLEH1QHqhY1BLdBc3ErO4h/TmEUgtj9EK6.7V8ajmQrEBqlMsEfDQBNw2my3BBsGA.iEp5b3WnnNk1fu4.iW1VtV5OWyPYU16FfpSXWokuJ5IhuAvsN0yppWGvaYx*lwFk!/b&bo=DQK8Ag0CvAICl7M!&rf=viewer_4",description:"少数民族风情"},{id:3,name:"吉卜力",preview:"http://m.qpic.cn/psc?/V539rQMC0OrLEH1QHqhY1BLdBc3ErO4h/TmEUgtj9EK6.7V8ajmQrEBqlMsEfDQBNw2my3BBsGA.NCOFh.MwcX7npzLGUKR92sFytGDy9qeHvROY4Jj0rllupPtTWG3uurkNgk0qtJBw!/b&bo=AAQABgAEAAYCByM!&rf=viewer_4",description:"吉卜力画风"},{id:4,name:"折纸",preview:"http://m.qpic.cn/psc?/V539rQMC0OrLEH1QHqhY1BLdBc3ErO4h/TmEUgtj9EK6.7V8ajmQrEBqlMsEfDQBNw2my3BBsGA*LkpeK3HlN6NsZMzAyBMsvcMmVNU8cG4*xbsBM32f1tOckZwxeJBv4vE*G2nDTLwU!/b&bo=AAMABAADAAQCByM!&rf=viewer_4",description:"折纸艺术"}],"3D":[{id:1,name:"粘土风",preview:"/static/style/clay.gif",description:"可爱卡通黏土风格"},{id:2,name:"民族风",preview:"/static/style/national.gif",description:"少数民族风情"},{id:3,name:"未来风",preview:"/static/ai/3d_future.png",description:"未来主义风格"},{id:4,name:"简约风",preview:"/static/ai/3d_minimal.png",description:"简洁现代设计"}],"动漫":[{id:1,name:"日系萌",preview:"/static/ai/anime_kawaii.png",description:"日式可爱风格"},{id:2,name:"少女风",preview:"/static/ai/anime_girl.png",description:"清新少女感"},{id:3,name:"热血风",preview:"/static/ai/anime_hot.png",description:"热血动漫风"},{id:4,name:"治愈系",preview:"/static/ai/anime_heal.png",description:"温暖治愈感"}],"手办":[{id:1,name:"精致款",preview:"/static/ai/figure_deluxe.png",description:"精工细作款式"},{id:2,name:"Q版款",preview:"/static/ai/figure_q.png",description:"Q萌可爱造型"},{id:3,name:"限定款",preview:"/static/ai/figure_limited.png",description:"限量珍藏版"},{id:4,name:"经典款",preview:"/static/ai/figure_classic.png",description:"经典传统造型"}],"写实":[{id:1,name:"自然系",preview:"/static/ai/real_nature.png",description:"自然真实感"},{id:2,name:"优雅系",preview:"/static/ai/real_elegant.png",description:"优雅知性风"},{id:3,name:"活力系",preview:"/static/ai/real_active.png",description:"青春活力感"},{id:4,name:"成熟系",preview:"/static/ai/real_mature.png",description:"成熟稳重风"}],"机甲":[{id:1,name:"战斗型",preview:"/static/ai/mecha_battle.png",description:"重装战斗机甲"},{id:2,name:"敏捷型",preview:"/static/ai/mecha_speed.png",description:"轻型高速机甲"},{id:3,name:"防御型",preview:"/static/ai/mecha_defense.png",description:"重甲防护型"},{id:4,name:"辅助型",preview:"/static/ai/mecha_support.png",description:"多功能辅助型"}],"宠物":[{id:1,name:"猫咪",preview:"/static/ai/pet_cat.png",description:"可爱小猫咪"},{id:2,name:"小狗",preview:"/static/ai/pet_dog.png",description:"忠诚小伙伴"},{id:3,name:"兔子",preview:"/static/ai/pet_rabbit.png",description:"软萌小兔子"},{id:4,name:"鸟类",preview:"/static/ai/pet_bird.png",description:"聪明小鸟儿"}]}),b=a(null);a(["#FF9B7A","#10D07A","#4FB3F0","#F5D547","#3B9AE6","#B8C5D1","#F5A3C7","#B084E6","#7BC96E"]),a({name:"kiki",color:"#10D07A",hair:"curly",cheeks:"rosy",eyes:"round"});const y=()=>{_()};return(e,i)=>{const a=p,_=n,k=f;return r(),t(_,{class:"style-container"},{default:s(()=>[c(_,{class:"header"},{default:s(()=>[c(_,{class:"back-btn",onClick:y},{default:s(()=>[c(a,{class:"back-icon"},{default:s(()=>[l("‹")]),_:1})]),_:1}),c(_,{class:"tabs"},{default:s(()=>[(r(!0),d(o,null,m(E.value,e=>(r(),t(_,{key:e,class:u(["tab",{active:g.value===e}]),onClick:i=>(e=>{g.value=e})(e)},{default:s(()=>[c(a,{class:"tab-text"},{default:s(()=>[l(v(e),1)]),_:2},1024)]),_:2},1032,["class","onClick"]))),128))]),_:1})]),_:1}),c(_,{style:{display:"flex","justify-content":"center","align-items":"center","z-index":"1","margin-top":"20rpx"}},{default:s(()=>[c(_,{class:"rabbit-container"},{default:s(()=>[c(k,{src:w,class:"animated-rabbit",style:{width:"400rpx",height:"600rpx"},mode:"aspectFit"})]),_:1})]),_:1}),c(_,{class:"features-section"},{default:s(()=>[c(_,{class:"feature-tabs"},{default:s(()=>[(r(!0),d(o,null,m(A.value,e=>(r(),t(_,{key:e,class:u(["feature-tab",{active:B.value===e}]),onClick:i=>(e=>{B.value=e,b.value=null})(e)},{default:s(()=>[c(a,{class:"feature-text"},{default:s(()=>[l(v(e),1)]),_:2},1024)]),_:2},1032,["class","onClick"]))),128))]),_:1}),c(_,{class:"options-grid"},{default:s(()=>[(r(!0),d(o,null,m(h.value[B.value],e=>{var i;return r(),t(_,{key:e.id,class:u(["option-item",{selected:(null==(i=b.value)?void 0:i.id)===e.id}]),onClick:i=>(e=>{b.value=e})(e)},{default:s(()=>[c(_,{class:"option-preview"},{default:s(()=>[c(k,{src:e.preview,class:"preview-image",mode:"widthFix"},null,8,["src"])]),_:2},1024),c(_,{class:"option-info"},{default:s(()=>[c(a,{class:"option-description"},{default:s(()=>[l(v(e.description),1)]),_:2},1024)]),_:2},1024)]),_:2},1032,["class","onClick"])}),128))]),_:1})]),_:1})]),_:1})}}}),[["__scopeId","data-v-8af15632"]]);export{B as default};
