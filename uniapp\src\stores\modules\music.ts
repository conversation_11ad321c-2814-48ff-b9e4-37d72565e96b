import { defineStore } from 'pinia'
import { ref } from 'vue'

// 定义音乐Store
export const useMusicStore = defineStore(
  'music',
  () => {
    // 当前音乐文件路径
    const currentMusicPath = ref<string>('/static/music/bg.mp3')
    
    // 音乐是否启用（用户设置，持久化）
    const isEnabled = ref<boolean>(true)
    
    // 音乐是否正在播放（运行时状态，不持久化）
    const isPlaying = ref<boolean>(false)
    
    // 音频上下文（运行时状态，不持久化）
    const audioContext = ref<UniApp.InnerAudioContext | null>(null)

    // 初始化音频上下文
    const initAudio = () => {
      if (audioContext.value) {
        audioContext.value.destroy()
      }
      
      try {
        audioContext.value = uni.createInnerAudioContext()
        audioContext.value.src = currentMusicPath.value
        audioContext.value.loop = true
        audioContext.value.volume = 0.3
        
        // 监听播放事件
        audioContext.value.onPlay(() => {
          isPlaying.value = true
          console.log('✅ 背景音乐开始播放')
        })
        
        // 监听暂停事件
        audioContext.value.onPause(() => {
          isPlaying.value = false
          console.log('⏸️ 背景音乐暂停')
        })
        
        // 监听停止事件
        audioContext.value.onStop(() => {
          isPlaying.value = false
          console.log('⏹️ 背景音乐停止')
        })
        
        // 监听错误事件
        audioContext.value.onError((error) => {
          console.error('❌ 背景音乐播放错误:', error)
          isPlaying.value = false
        })
        
        console.log('🎵 音频上下文初始化完成')
      } catch (error) {
        console.error('❌ 音频上下文初始化失败:', error)
      }
    }

    // 播放音乐
    const play = () => {
      if (!isEnabled.value) {
        console.log('🔇 音乐已禁用，跳过播放')
        return
      }
      
      if (!audioContext.value) {
        initAudio()
      }
      
      if (audioContext.value && !isPlaying.value) {
        try {
          audioContext.value.play()
          console.log('🎶 开始播放背景音乐')
        } catch (error) {
          console.error('❌ 播放音乐失败:', error)
        }
      }
    }

    // 暂停音乐
    const pause = () => {
      if (audioContext.value && isPlaying.value) {
        try {
          audioContext.value.pause()
          console.log('⏸️ 暂停背景音乐')
        } catch (error) {
          console.error('❌ 暂停音乐失败:', error)
        }
      }
    }

    // 停止音乐
    const stop = () => {
      if (audioContext.value) {
        try {
          audioContext.value.stop()
          console.log('⏹️ 停止背景音乐')
        } catch (error) {
          console.error('❌ 停止音乐失败:', error)
        }
      }
    }

    // 切换音乐播放状态
    const toggleMusic = () => {
      if (isPlaying.value) {
        pause()
      } else {
        play()
      }
    }

    // 设置音乐启用状态
    const setEnabled = (enabled: boolean) => {
      isEnabled.value = enabled
      console.log(`🎵 音乐${enabled ? '启用' : '禁用'}`)
      
      if (enabled) {
        play()
      } else {
        stop()
      }
    }

    // 自动启动音乐（应用启动时调用）
    const autoStart = () => {
      console.log('🚀 自动启动背景音乐')
      
      // 重置播放状态
      isPlaying.value = false
      
      // 初始化音频
      initAudio()
      
      // 如果音乐启用，延迟播放确保初始化完成
      if (isEnabled.value) {
        setTimeout(() => {
          if (audioContext.value && !isPlaying.value) {
            audioContext.value.play()
            console.log('🎶 自动播放命令已发送')
          }
        }, 500)
      }
    }

    // 重置运行时状态（应用启动时调用）
    const resetRuntimeState = () => {
      console.log('🔄 重置音乐运行时状态')
      isPlaying.value = false
      if (audioContext.value) {
        audioContext.value.destroy()
        audioContext.value = null
      }
    }

    // 应用退出时清理（应用隐藏时调用）
    const onAppExit = () => {
      console.log('🚪 应用退出，清理音乐状态')
      
      // 停止并销毁音频
      if (audioContext.value) {
        if (isPlaying.value) {
          audioContext.value.stop()
        }
        audioContext.value.destroy()
        audioContext.value = null
      }
      
      // 重置状态
      isEnabled.value = false
      isPlaying.value = false
    }

    return {
      // 状态
      currentMusicPath,
      isEnabled,
      isPlaying,
      audioContext,
      
      // 方法
      initAudio,
      play,
      pause,
      stop,
      toggleMusic,
      setEnabled,
      autoStart,
      resetRuntimeState,
      onAppExit
    }
  },
  {
    persist: {
      key: 'music-store',
      storage: {
        getItem: (key: string) => uni.getStorageSync(key),
        setItem: (key: string, value: string) => uni.setStorageSync(key, value),
        removeItem: (key: string) => uni.removeStorageSync(key)
      },
      // 只持久化用户设置，运行时状态不持久化
      paths: ['isEnabled']
    }
  }
)