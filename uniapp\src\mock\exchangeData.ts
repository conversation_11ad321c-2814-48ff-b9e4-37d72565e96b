import type { Post, Comment } from '@/types/community'

// 社区交流模拟数据
export const mockExchangeData: {
  posts: Record<string, Post[]>
  comments: Record<number, Comment[]>
} = {
  // 帖子列表数据
  posts: {
    all: [
      {
        id: 1,
        title: "今天心情特别好，分享一下我的小确幸",
        content: "早上起来看到阳光透过窗帘洒进房间，突然觉得生活真美好。昨天晚上和朋友聊天到很晚，聊了很多关于未来的规划，感觉充满了希望。今天准备去公园走走，享受这难得的好天气。",
        authorName: "阳光小雨",
        authorAvatar: "https://picsum.photos/100/100?random=1",
        categoryName: "心情感悟",
        createTime: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
        likeCount: 23,
        commentCount: 8,
        isLiked: false,
        images: [
          "https://picsum.photos/400/300?random=101",
          "https://picsum.photos/400/300?random=102"
        ]
      },
      {
        id: 2,
        title: "学习Vue3的一些心得体会",
        content: "最近在学习Vue3，发现Composition API真的很强大。相比Vue2的Options API，代码组织更加灵活，逻辑复用也更方便。特别是ref和reactive的使用，让状态管理变得更加直观。分享一些学习资源给大家。",
        authorName: "代码小白",
        authorAvatar: "https://picsum.photos/100/100?random=2",
        categoryName: "学习交流",
        createTime: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
        likeCount: 45,
        commentCount: 12,
        isLiked: true,
        images: [
          "https://picsum.photos/400/300?random=103"
        ]
      },
      {
        id: 3,
        title: "求助：如何平衡工作和生活？",
        content: "最近工作压力很大，经常加班到很晚，感觉没有时间陪家人和朋友。想问问大家是怎么平衡工作和生活的？有什么好的建议吗？特别是如何在忙碌的工作中保持身心健康。",
        authorName: "忙碌的蜜蜂",
        authorAvatar: "https://picsum.photos/100/100?random=3",
        categoryName: "问题求助",
        createTime: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
        likeCount: 18,
        commentCount: 25,
        isLiked: false,
        images: []
      },
      {
        id: 4,
        title: "分享一个超好用的时间管理方法",
        content: "最近发现了番茄工作法，真的太好用了！25分钟专注工作，5分钟休息，每4个番茄后休息15-30分钟。这样不仅提高了工作效率，还能保持精力充沛。推荐给经常拖延的朋友们试试。",
        authorName: "效率达人",
        authorAvatar: "https://picsum.photos/100/100?random=4",
        categoryName: "技能分享",
        createTime: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString(),
        likeCount: 67,
        commentCount: 15,
        isLiked: true,
        images: [
          "https://picsum.photos/400/300?random=104",
          "https://picsum.photos/400/300?random=105",
          "https://picsum.photos/400/300?random=106"
        ]
      },
      {
        id: 5,
        title: "今天做了一顿丰盛的晚餐",
        content: "周末在家尝试做了红烧肉、蒸蛋羹和青菜，虽然卖相一般，但味道还不错。做饭真的是一件很治愈的事情，看着食材在手中变成美味的菜肴，很有成就感。下次想挑战一下更复杂的菜。",
        authorName: "厨房新手",
        authorAvatar: "https://picsum.photos/100/100?random=5",
        categoryName: "日常分享",
        createTime: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString(),
        likeCount: 34,
        commentCount: 9,
        isLiked: false,
        images: [
          "https://picsum.photos/400/300?random=107",
          "https://picsum.photos/400/300?random=108",
          "https://picsum.photos/400/300?random=109",
          "https://picsum.photos/400/300?random=110"
        ]
      }
    ],
    daily: [
      {
        id: 6,
        title: "早起看日出的美好体验",
        content: "今天5点半起床去看日出，虽然很困但是看到太阳慢慢升起的那一刻，觉得一切都值得了。清晨的空气特别清新，鸟儿在枝头歌唱，整个世界都显得那么宁静美好。",
        authorName: "早起鸟儿",
        authorAvatar: "https://picsum.photos/100/100?random=6",
        categoryName: "日常分享",
        createTime: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(),
        likeCount: 28,
        commentCount: 6,
        isLiked: false,
        images: [
          "https://picsum.photos/400/300?random=111"
        ]
      },
      {
        id: 7,
        title: "和朋友一起逛街买到心仪的衣服",
        content: "今天和闺蜜一起去商场逛街，终于买到了心仪已久的那件外套。试穿的时候朋友说特别适合我，心情瞬间变好了。逛街真的是女生最好的解压方式之一。",
        authorName: "购物小达人",
        authorAvatar: "https://picsum.photos/100/100?random=7",
        categoryName: "日常分享",
        createTime: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString(),
        likeCount: 19,
        commentCount: 4,
        isLiked: true,
        images: [
          "https://picsum.photos/400/300?random=112",
          "https://picsum.photos/400/300?random=113"
        ]
      }
    ],
    mood: [
      {
        id: 8,
        title: "突然想起小时候的那些美好时光",
        content: "今天路过小学门口，看到孩子们放学的场景，突然想起了自己的童年。那时候没有手机，没有网络，但是有最纯真的快乐。和小伙伴一起玩游戏，一起做作业，一起分享零食，那些简单的快乐现在想起来还是那么温暖。",
        authorName: "怀旧少年",
        authorAvatar: "https://picsum.photos/100/100?random=8",
        categoryName: "心情感悟",
        createTime: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
        likeCount: 42,
        commentCount: 16,
        isLiked: false,
        images: []
      },
      {
        id: 9,
        title: "失恋后的一些感悟",
        content: "分手已经一个月了，从最初的痛苦到现在的平静，我学会了很多。爱情不是生活的全部，我们要学会爱自己，学会独立。虽然还是会想念，但我知道这是成长必经的路。感谢那段美好的回忆，也感谢现在更强大的自己。",
        authorName: "重新出发",
        authorAvatar: "https://picsum.photos/100/100?random=9",
        categoryName: "心情感悟",
        createTime: new Date(Date.now() - 5 * 60 * 60 * 1000).toISOString(),
        likeCount: 56,
        commentCount: 23,
        isLiked: true,
        images: [
          "https://picsum.photos/400/300?random=114"
        ]
      }
    ],
    study: [
      {
        id: 10,
        title: "考研复习的一些经验分享",
        content: "考研已经进入冲刺阶段，分享一些复习经验。首先要制定详细的复习计划，其次要保持良好的作息，最重要的是要坚持。虽然过程很辛苦，但想到未来的目标就有了动力。加油，所有考研的朋友们！",
        authorName: "考研战士",
        authorAvatar: "https://picsum.photos/100/100?random=10",
        categoryName: "学习交流",
        createTime: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(),
        likeCount: 73,
        commentCount: 28,
        isLiked: true,
        images: [
          "https://picsum.photos/400/300?random=115",
          "https://picsum.photos/400/300?random=116"
        ]
      }
    ],
    skill: [
      {
        id: 11,
        title: "Photoshop修图技巧分享",
        content: "最近学了一些PS修图技巧，特别是人像修图。分享几个实用的技巧：1.用修复画笔工具去除瑕疵 2.用曲线调整明暗对比 3.用色彩平衡调整肤色。希望对喜欢摄影的朋友有帮助。",
        authorName: "修图师小王",
        authorAvatar: "https://picsum.photos/100/100?random=11",
        categoryName: "技能分享",
        createTime: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString(),
        likeCount: 89,
        commentCount: 31,
        isLiked: false,
        images: [
          "https://picsum.photos/400/300?random=117",
          "https://picsum.photos/400/300?random=118",
          "https://picsum.photos/400/300?random=119"
        ]
      }
    ],
    help: [
      {
        id: 12,
        title: "求推荐好用的学习APP",
        content: "最近想提升自己，求推荐一些好用的学习APP。主要想学英语和编程，希望有经验的朋友能推荐一些靠谱的应用。最好是免费或者性价比高的，谢谢大家！",
        authorName: "学习小白",
        authorAvatar: "https://picsum.photos/100/100?random=12",
        categoryName: "问题求助",
        createTime: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
        likeCount: 35,
        commentCount: 42,
        isLiked: false,
        images: []
      }
    ],
    other: [
      {
        id: 13,
        title: "推荐一部最近看的好电影",
        content: "最近看了《你好，李焕英》，真的被感动哭了。电影讲述了女儿穿越回过去想让妈妈过得更好的故事，但最后发现妈妈最大的愿望就是女儿健康快乐。珍惜身边的人，特别是我们的父母。",
        authorName: "电影爱好者",
        authorAvatar: "https://picsum.photos/100/100?random=13",
        categoryName: "其他",
        createTime: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
        likeCount: 64,
        commentCount: 18,
        isLiked: true,
        images: [
          "https://picsum.photos/400/300?random=120"
        ]
      }
    ]
  },

  // 评论数据
  comments: {
    1: [
      {
        id: 101,
        content: "看到你的分享也感觉心情变好了，正能量满满！",
        authorName: "快乐小天使",
        authorAvatar: "https://picsum.photos/100/100?random=21",
        createTime: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString()
      },
      {
        id: 102,
        content: "是啊，有时候简单的小事就能带来很大的快乐",
        authorName: "简单生活",
        authorAvatar: "https://picsum.photos/100/100?random=22",
        createTime: new Date(Date.now() - 30 * 60 * 1000).toISOString()
      }
    ],
    2: [
      {
        id: 103,
        content: "Vue3确实很强大，我也在学习中，一起加油！",
        authorName: "前端小菜鸟",
        authorAvatar: "https://picsum.photos/100/100?random=23",
        createTime: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString()
      }
    ]
  }
}