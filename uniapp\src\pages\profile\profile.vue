<script setup lang="ts">
import { ref } from 'vue'
import { useUserStore } from '@/stores'
import { updateNickname, updatePartner, updatePhone } from '@/services/profile'
import { http } from '@/utils/http'

const userStore = useUserStore()

// 响应式数据
const profileData = ref({
  locationOn: false,
  planetMusicOn: false
})

// 弹框相关数据
const showEditDialog = ref(false)
const editType = ref('')
const editValue = ref('')
const editTitle = ref('')

// 删除星灵弹框相关数据
const showDeleteDialog = ref(false)
const deleteInputValue = ref('')
const showFinalConfirm = ref(false)

// 方法
const togglePlanetLocation = () => {
  uni.showToast({
    title: '敬请期待',
    icon: 'none'
  })
  profileData.value.locationOn = !profileData.value.locationOn
}

// 方法
const togglePlanetMusic = () => {
  uni.showToast({
    title: '敬请期待',
    icon: 'none'
  })
  profileData.value.planetMusicOn = !profileData.value.planetMusicOn
}

const openNotebook = () => {
  uni.showToast({
    title: '敬请期待',
    icon: 'none'
  })
}

const restorePurchases = () => {
  console.log('恢复购买')
}

const goBack = () => {
  if (getCurrentPages().length == 1) {
    uni.reLaunch({ url: '/pages/ai/ai' })
  } else {
    uni.navigateBack({ delta: 1})
  }
}

// 编辑相关方法
const openEditDialog = (type: string) => {
  editType.value = type
  switch (type) {
    case 'name':
      editTitle.value = '修改昵称'
      editValue.value = userStore.profile.nickname
      break
    case 'friendName':
      editTitle.value = '修改伙伴'
      editValue.value = userStore.profile.partner.nickName
      break
    case 'phone':
      editTitle.value = '修改联系号码'
      editValue.value = userStore.profile.phone
      break
  }
  showEditDialog.value = true
}

const closeEditDialog = () => {
  showEditDialog.value = false
  editType.value = ''
  editValue.value = ''
  editTitle.value = ''
}

const saveEdit = async () => {
  if (!editValue.value.trim()) {
    uni.showToast({
      title: '请输入内容',
      icon: 'none'
    })
    return
  }

  // 显示加载提示
  uni.showLoading({
    title: '保存中...'
  })

  try {
    switch (editType.value) {
      case 'name':
        // 调用更新昵称接口
        const nicknameRes = await updateNickname(editValue.value.trim())
        if (nicknameRes.code === 200) {
          profileData.value.name = editValue.value.trim()
          // 更新用户store中的昵称
          userStore.profile.nickname = editValue.value.trim()
          uni.showToast({
            title: '已保存',
            icon: 'success'
          })
        } else {
          uni.showToast({
            title: nicknameRes.msg || '更新失败',
            icon: 'none'
          })
          return
        }
        break
      case 'friendName':
        console.log('...');
        
        const partnerRes = await updatePartner(editValue.value.trim())
        if (partnerRes.code === 200) {
          profileData.value.friendName = editValue.value.trim()
          // 更新用户store中的昵称
          userStore.profile.partner.nickName = editValue.value.trim()
          uni.showToast({
            title: '已保存',
            icon: 'success'
          })
        } else {
          uni.showToast({
            title: partnerRes.msg || '更新失败',
            icon: 'none'
          })
          return
        }
        break
      case 'phone':
        // 调用更新联系号码接口
        const phoneRes = await updatePhone(editValue.value.trim())
        if (phoneRes.code === 200) {
          profileData.value.phone = editValue.value.trim()
          // 更新用户store中的手机号
          userStore.profile.mobile = editValue.value.trim()
          uni.showToast({
            title: '联系号码更新成功',
            icon: 'success'
          })
        } else {
          uni.showToast({
            title: phoneRes.msg || '更新失败',
            icon: 'none'
          })
          return
        }
        break
    }
    
    closeEditDialog()
  } catch (error) {
    console.error('更新失败:', error)
    uni.showToast({
      title: '网络错误，请稍后重试',
      icon: 'none'
    })
  } finally {
    uni.hideLoading()
  }
}

const delPartner = () => {
  // 检查是否有星灵
  // if (!userStore.profile?.partner?.nickName) {
  //   uni.showToast({
  //     title: '暂无星灵可删除',
  //     icon: 'none'
  //   })
  //   return
  // }

  // 显示删除确认弹框
  deleteInputValue.value = ''
  showDeleteDialog.value = true
}

const closeDeleteDialog = () => {
  showDeleteDialog.value = false
  showFinalConfirm.value = false
  deleteInputValue.value = ''
}

const confirmDelete = () => {
  const partnerName = userStore.profile?.partner?.nickName
  
  if (deleteInputValue.value.trim() === partnerName) {
    // 名称正确，显示最终确认
    showFinalConfirm.value = true
  } else {
    uni.showToast({
      title: '星灵名称输入错误',
      icon: 'none'
    })
  }
}

const finalConfirmDelete = () => {
  closeDeleteDialog()
  performDeletePartner()
}

const performDeletePartner = async () => {
  uni.showLoading({
    title: '删除中...'
  })
  
  try {
    // 这里调用删除星灵的API
    const res = await http({
      // url: '/partner/delete'
    })
    if (res.code === 200) {
      // 删除成功，清空用户store中的星灵信息
      userStore.profile.partner = null
      uni.showToast({
        title: '星灵已删除',
        icon: 'success'
      })
    } else {
      uni.showToast({
        title: res.msg || '删除失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('删除星灵失败:', error)
    uni.showToast({
      title: '网络错误，请稍后重试',
      icon: 'none'
    })
  } finally {
    uni.hideLoading()
  }
}
</script>

<template>
  <view class="profile-container">
    <!-- 顶部导航栏 -->
    <view class="header">
      <view class="back-btn" @click="goBack">
        <text class="back-icon">‹</text>
      </view>
      <view class="title-container">
        <text class="title">个人资料</text>
      </view>
    </view>

    <!-- 个人资料内容 -->
    <view class="profile-content">
      <!-- 头像部分 -->
      <view class="profile-item">
        <text class="item-label">头像</text>
        <view class="avatar-container">
          <image 
            class="avatar" 
            :src="userStore.profile?.avatar" 
            mode="aspectFill"
          />
        </view>
      </view>

      <!-- 姓名 -->
      <view class="profile-item clickable" @click="openEditDialog('name')">
        <text class="item-label">昵称</text>
        <text class="item-value">{{ userStore.profile.nickname }}</text>
      </view>

      <!-- 代词 -->
      <!-- <view class="profile-item">
        <text class="item-label">代词</text>
        <text class="item-value placeholder">{{ profileData.pronouns || '' }}</text>
      </view> -->

      <!-- 朋友名字 -->
      <view class="profile-item clickable" @click="openEditDialog('friendName')">
        <text class="item-label">星灵</text>
        <text class="item-value">{{ userStore.profile?.partner ? userStore.profile.partner.nickName :'暂无' }}</text>
      </view>

      <!-- 笔记本解锁 -->
      <view class="profile-item clickable" @click="openNotebook">
        <text class="item-label">笔记本解锁</text>
        <view class="unlock-icon">
          <text class="icon">🔓</text>
        </view>
      </view>

      <!-- 地图展示 -->
      <view class="profile-item">
        <view class="music-content">
          <view class="music-info">
            <view class="item-label">地图展示</view>
            <view class="item-description">把你的星灵展示在地图上，不会显示个人信息</view>
          </view>
          <view class="switch-container">
            <switch 
              :checked="profileData.locationOn" 
              @change="togglePlanetLocation"
              color="#4CD964"
            />
            <text class="switch-status">{{ profileData.locationOn ? 'ON' : 'OFF' }}</text>
          </view>
        </view>
      </view>

      <!-- 星球音乐 -->
      <view class="profile-item">
        <view class="music-content">
          <view class="music-info">
            <view class="item-label">星球音乐</view>
            <view class="item-description">当你不和{{ userStore.profile?.partner ? userStore.profile?.partner.nickName : '星灵' }}谈话时，播放舒缓的音乐。</view>
          </view>
          <view class="switch-container">
            <switch 
              :checked="profileData.planetMusicOn" 
              @change="togglePlanetMusic"
              color="#4CD964"
            />
            <text class="switch-status">{{ profileData.planetMusicOn ? 'ON' : 'OFF' }}</text>
          </view>
        </view>
      </view>

      <!-- 电话 -->
      <view class="profile-item clickable" @click="openEditDialog('phone')">
        <view class="phone-content">
          <view class="item-label">联系号码</view>
          <view class="item-description">您的电话号码已验证</view>
        </view>
        <text class="phone-number">{{ userStore.profile?.phone }}</text>
      </view>

      <view @click="delPartner" style="width:100%;text-align:center;margin-top: 50rpx;color:#cc3434;font-size:large;font-weight:bold;">删除星灵</view>

      <!-- 恢复购买 -->
      <!-- <view class="profile-item clickable" @click="restorePurchases">
        <text class="item-label">Restore purchases</text>
      </view> -->
    </view>

    <!-- 编辑弹框 -->
    <view v-if="showEditDialog" class="dialog-overlay" @click="closeEditDialog">
      <view class="dialog-container" @click.stop>
        <view class="dialog-header">
          <text class="dialog-title">{{ editTitle }}</text>
          <view class="cancel-btn" @click="closeEditDialog">
            <image class="cancel-icon" src="/static/ai/cancel.png" mode="aspectFit" />
          </view>
        </view>
        <view class="dialog-content">
          <input 
            v-model="editValue" 
            class="dialog-input"
            :placeholder="editTitle"
            :type="editType === 'phone' ? 'number' : 'text'"
            :maxlength="editType === 'phone' ? 11 : 20"
          />
        </view>
        <view class="dialog-actions">
          <button class="dialog-btn save-btn" @click="saveEdit">保存</button>
        </view>
      </view>
    </view>

    <!-- 删除星灵确认弹框 -->
    <view v-if="showDeleteDialog" class="dialog-overlay" @click="closeDeleteDialog">
      <view class="delete-dialog-container" @click.stop>
        <view class="delete-dialog-header">
          <text class="delete-dialog-title">删除星灵</text>
          <view class="cancel-btn" @click="closeDeleteDialog">
            <text class="cancel-text">✕</text>
          </view>
        </view>
        
        <view v-if="!showFinalConfirm" class="delete-dialog-content">
          <view class="warning-icon">
            <text class="warning-text">⚠️</text>
          </view>
          <view class="delete-message">
            <text class="message-text">请输入星灵名称以确认删除</text>
            <text class="partner-name">"{{ userStore.profile?.partner?.nickName }}"</text>
          </view>
          <input 
            v-model="deleteInputValue" 
            class="delete-input"
            placeholder="请输入星灵名称"
            maxlength="20"
          />
          <view class="delete-actions">
            <button class="delete-btn cancel-btn-style" @click="closeDeleteDialog">取消</button>
            <button class="delete-btn confirm-btn-style" @click="confirmDelete">确认</button>
          </view>
        </view>

        <view v-else class="delete-dialog-content">
          <view class="danger-icon">
            <text class="danger-text">🚨</text>
          </view>
          <view class="final-message">
            <text class="final-text">最终确认</text>
            <text class="final-warning">删除后无法恢复，确定要删除星灵吗？</text>
          </view>
          <view class="final-actions">
            <button class="delete-btn cancel-btn-style" @click="showFinalConfirm = false">返回</button>
            <button class="delete-btn danger-btn-style" @click="finalConfirmDelete">确定删除</button>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
.profile-container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-top: env(safe-area-inset-top);
}

.header {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  padding: 20rpx 20px;
  background-color: white;
  border-bottom: 1px solid #e5e5e5;

  .back-btn {
    position: absolute;
    left: 20px;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;

    .back-icon {
      font-size: 50px;
      color: #333;
      font-weight: bold;
    }
  }

  .title-container {
    border: 2px solid #333;
    border-radius: 25px;
    padding: 4px 24px;
    margin-top: 10px;

    .title {
      font-size: 16px;
      font-weight: 600;
      color: #333;
    }
  }
}

.profile-content {
  padding: 0;
}

.profile-item {
  background-color: white;
  border-bottom: 1px solid #e5e5e5;
  padding: 40rpx 60rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;

  &.clickable {
    &:active {
      background-color: #f0f0f0;
    }
  }

  .item-label {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    flex: 1;
  }

  .item-value {
    font-size: 16px;
    color: #666;
    text-align: right;

    &.placeholder {
      color: #ccc;
    }
  }

  .item-description {
    font-size: 14px;
    color: #999;
    margin-top: 4px;
    line-height: 1.4;
  }
}

.avatar-container {
  width: 50px;
  height: 50px;
  border-radius: 30px;
  overflow: hidden;
  background: linear-gradient(45deg, #8B4513, #2F4F4F);
  display: flex;
  align-items: center;
  justify-content: center;

  .avatar {
    width: 100%;
    height: 100%;
  }
}

.music-content {
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: space-between;

  .music-info {
    flex: 1;
    margin-right: 20px;
  }

  .switch-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;

    .switch-status {
      font-size: 12px;
      color: #4CD964;
      font-weight: 600;
    }
  }
}

.unlock-icon {
  .icon {
    font-size: 20px;
  }
}

.phone-content {
  flex: 1;
  margin-right: 20px;
}

.phone-number {
  font-size: 16px;
  color: #666;
  font-weight: 500;
}

/* uni-app 特定样式调整 */
switch {
  transform: scale(0.8);
}

/* 弹框样式 */
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.dialog-container {
  background-color: white;
  border-radius: 12px;
  width: 80%;
  max-width: 400px;
  margin: 0 20px;
  overflow: hidden;
}

.dialog-header {
  padding: 20px;
  border-bottom: 1px solid #e5e5e5;
  text-align: center;
  position: relative;

  .dialog-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
  }

  .cancel-btn {
    position: absolute;
    top: 20px;
    right: 20px;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;

    .cancel-icon {
      width: 30px;
      height: 30px;
    }

    &:active {
      opacity: 0.7;
    }
  }
}

.dialog-content {
  padding: 20px;

  .dialog-input {
    width: 90%;
    height: 44px;
    border: 1px solid #e5e5e5;
    border-radius: 8px;
    padding: 0 12px;
    font-size: 16px;
    color: #333;
    background-color: #f8f8f8;

    &:focus {
      border-color: #4CD964;
      background-color: white;
    }
  }
}

.dialog-actions {
  padding: 20px;

  .dialog-btn {
    width: 100%;
    height: 50px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;

    &.save-btn {
      background-color: #333;
      color: white;

      &:active {
        background-color: #555;
      }
    }
  }
}

/* 删除星灵弹框样式 */
.delete-dialog-container {
  background-color: white;
  border-radius: 16px;
  width: 85%;
  max-width: 420px;
  margin: 0 20px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.delete-dialog-header {
  padding: 24px 20px 16px;
  text-align: center;
  position: relative;
  background: linear-gradient(135deg, #ff6b6b, #ee5a52);

  .delete-dialog-title {
    font-size: 20px;
    font-weight: 700;
    color: white;
  }

  .cancel-btn {
    position: absolute;
    top: 20px;
    right: 20px;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    cursor: pointer;

    .cancel-text {
      font-size: 18px;
      color: white;
      font-weight: bold;
    }

    &:active {
      background-color: rgba(255, 255, 255, 0.3);
    }
  }
}

.delete-dialog-content {
  padding: 30px 24px;
  text-align: center;

  .warning-icon, .danger-icon {
    margin-bottom: 20px;

    .warning-text, .danger-text {
      font-size: 48px;
    }
  }

  .delete-message {
    margin-bottom: 24px;

    .message-text {
      display: block;
      font-size: 16px;
      color: #666;
      margin-bottom: 8px;
      line-height: 1.5;
    }

    .partner-name {
      display: block;
      font-size: 18px;
      font-weight: 700;
      color: #ff6b6b;
      background-color: #fff5f5;
      padding: 8px 16px;
      border-radius: 8px;
      border: 2px solid #ffe0e0;
    }
  }

  .delete-input {
    width: 87%;
    height: 48px;
    border: 2px solid #e5e5e5;
    border-radius: 12px;
    padding: 0 16px;
    font-size: 16px;
    color: #333;
    background-color: #fafafa;
    margin-bottom: 24px;
    text-align: center;
    font-weight: 500;

    &:focus {
      border-color: #ff6b6b;
      background-color: white;
      outline: none;
    }
  }

  .delete-actions, .final-actions {
    display: flex;
    gap: 12px;

    .delete-btn {
      flex: 1;
      height: 48px;
      border: none;
      border-radius: 12px;
      font-size: 16px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.2s ease;

      &.cancel-btn-style {
        background-color: #f5f5f5;
        color: #666;
        border: 2px solid #e5e5e5;

        &:active {
          background-color: #ebebeb;
        }
      }

      &.confirm-btn-style {
        background-color: #ff6b6b;
        color: white;

        &:active {
          background-color: #ff5252;
        }
      }

      &.danger-btn-style {
        background-color: #d32f2f;
        color: white;

        &:active {
          background-color: #b71c1c;
        }
      }
    }
  }

  .final-message {
    margin-bottom: 24px;

    .final-text {
      display: block;
      font-size: 20px;
      font-weight: 700;
      color: #d32f2f;
      margin-bottom: 12px;
    }

    .final-warning {
      display: block;
      font-size: 16px;
      color: #666;
      line-height: 1.5;
    }
  }
}
</style>