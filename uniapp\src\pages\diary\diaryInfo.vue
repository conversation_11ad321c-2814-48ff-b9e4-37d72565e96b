<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'

// 页面数据
const diaryData = ref({
  id: '',
  title: '煮可乐的方法',
  category: '品味阅读',
  image: '/static/diary/cooking-art.jpg',
  summary: '一句话',
  summaryContent: '熟练的',
  strengthTitle: '你的味觉强度',
  strengthContent: '烹饪完美主义者——你对烹饪的投入和对细节的关注与你的其他追求一样，创造出反映你创新精神的独特菜肴',
  focusTitle: '你关注的领域',
  focusContent: '分享你的创意——你的可乐煮鸡翅食谱展示了你制作独特风味的天赋。考虑记录和分享你的烹饪实验，以激励他人',
  content: '在你与食物的关系中，你对待生活本身的方式有着惊人的相似之处——精确、创造性和对完美的坚定不移的追求。你的招牌可乐煮鸡翅不仅仅是一道菜；他们证明了你有能力通过仔细的实验和改进，把熟悉的东西变成你独特的东西。\n\n你对品味的欣赏不仅仅是为了维持生计——它与你对有意义的经历和联系的价值密切相关。就像你珍惜与家人一起旅行，沉浸在美丽的风景中一样，你与食物的关系似乎就是创造值得回忆的时刻。你对待烹饪的方式有一种艺术性，让人想起你对美的更广泛的欣赏，无论是在大自然中还是在数字平台上发现的。\n\n特别引人注目的是，你的烹饪之旅是如何反映出你的职业心态的——你对完美的不懈追求，定义了你的职业道德，也体现在你的烹饪中。你不满足于简单地遵循食谱；你把它们变成你自己的，不断地提炼和改进，直到你达到你的目标。这种对精通的执着，加上你的创新精神，表明你在与他人分享你独特的烹饪创作方面有很多可以提供的。\n\n当你继续探索和完善你的品味冒险时，请记住，你对细节的关注和追求卓越的动力不仅仅是为了达到完美——它们是为了创造能给他人带来快乐和联系的体验。你的可乐煮鸡翅可能只是一段值得分享的美味旅程的开始。',
  createTime: '2024-01-15 22:22'
})

// 页面加载
onLoad((options) => {
  if (options.id) {
    diaryData.value.id = options.id
    // 这里可以根据ID加载具体的日记数据
    loadDiaryDetail(options.id)
  }
})

const loadDiaryDetail = async (id: string) => {
  // 模拟加载数据
  console.log('Loading diary detail:', id)
}

const goBack = () => {
  uni.navigateBack()
}

const shareDiary = () => {
  uni.showActionSheet({
    itemList: ['分享到微信', '分享到朋友圈', '复制链接'],
    success: (res) => {
      console.log('分享选项:', res.tapIndex)
    }
  })
}

const deleteDiary = () => {
  uni.showModal({
    title: '确认删除',
    content: '确定要删除这篇日记吗？',
    success: (res) => {
      if (res.confirm) {
        // 执行删除操作
        console.log('删除日记')
        uni.navigateBack()
      }
    }
  })
}
</script>

<template>
  <view class="diary-detail-container">
    <!-- 顶部图片区域 -->
    <view class="header-image-section">
      <view class="header-image-bg">
        <image
         style="width: 100%;"
            src="/static/diary1.png"
            mode="aspectFill"
        />
      </view>
      
      <!-- 顶部操作栏 -->
      <view class="header-actions">
        <view class="time-display" @click="goBack">
            <image
                style="width: 100%;height: 100%;"
                src="/static/ai/back2.png"
                mode="aspectFit"
            />
        </view>
        <view class="action-buttons">
          <!-- <view class="action-btn" @click="goBack">
            <text class="action-icon back-icon">‹</text>
          </view>
          <view class="action-btn" @click="shareDiary">
            <text class="action-icon share-icon">↗</text>
          </view>
          <view class="action-btn" @click="deleteDiary">
            <text class="action-icon delete-icon">🗑</text>
          </view> -->
        </view>
      </view>
    </view>

    <!-- 内容区域 -->
    <view class="content-section">
      <!-- 分类标签 -->
      <view class="category-tag">
        <text class="category-text">{{ diaryData.category }}</text>
      </view>

      <!-- 标题 -->
      <view class="title-section">
        <text class="main-title">{{ diaryData.title }}</text>
      </view>

      <!-- 摘要部分 -->
      <view class="summary-section">
        <text class="summary-label">{{ diaryData.summary }}</text>
        <text class="summary-content">{{ diaryData.summaryContent }}</text>
      </view>

      <!-- 详细内容 -->
      <view class="detail-content">
        <!-- 优势分析 -->
        <view class="content-block">
          <text class="block-title">{{ diaryData.strengthTitle }}</text>
          <text class="block-content">{{ diaryData.strengthContent }}</text>
        </view>

        <!-- 关注领域 -->
        <view class="content-block">
          <text class="block-title">{{ diaryData.focusTitle }}</text>
          <text class="block-content">{{ diaryData.focusContent }}</text>
        </view>
      </view>

      <view class="">
        <text>{{ diaryData.content }}</text>
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
.diary-detail-container {
  min-height: 100vh;
  background-color: #f8f8f8;
}

.header-image-section {
  position: relative;
  height: 400rpx;
  overflow: hidden;

  .header-image-bg {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #ff6b35 0%, #f7931e 25%, #2e8b57 50%, #4682b4 75%, #8b4513 100%);
    position: relative;
  }

  .art-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    overflow: hidden;

    .art-shape {
      position: absolute;
      border-radius: 50%;
      
      &.shape-1 {
        width: 120rpx;
        height: 120rpx;
        background: rgba(255, 255, 255, 0.2);
        top: 60rpx;
        left: 80rpx;
      }

      &.shape-2 {
        width: 80rpx;
        height: 80rpx;
        background: rgba(0, 0, 0, 0.1);
        top: 100rpx;
        right: 100rpx;
      }

      &.shape-3 {
        width: 60rpx;
        height: 60rpx;
        background: rgba(255, 255, 255, 0.15);
        bottom: 120rpx;
        left: 60rpx;
      }

      &.shape-4 {
        width: 100rpx;
        height: 100rpx;
        background: rgba(139, 69, 19, 0.3);
        bottom: 80rpx;
        right: 80rpx;
      }
    }

    .cooking-pot {
      position: absolute;
      bottom: 80rpx;
      left: 50%;
      transform: translateX(-50%);

      .pot-body {
        width: 120rpx;
        height: 80rpx;
        background: linear-gradient(145deg, #d4af37, #b8860b);
        border-radius: 0 0 60rpx 60rpx;
        border: 4rpx solid #8b7355;
        position: relative;
        
        &::before {
          content: '';
          position: absolute;
          top: -8rpx;
          left: 50%;
          transform: translateX(-50%);
          width: 100rpx;
          height: 16rpx;
          background: #8b7355;
          border-radius: 8rpx;
        }
      }

      .pot-handle {
        position: absolute;
        right: -20rpx;
        top: 20rpx;
        width: 16rpx;
        height: 40rpx;
        border: 3rpx solid #8b7355;
        border-left: none;
        border-radius: 0 20rpx 20rpx 0;
      }

      .steam {
        position: absolute;
        width: 8rpx;
        height: 30rpx;
        background: rgba(255, 255, 255, 0.6);
        border-radius: 4rpx;
        animation: steam-rise 2s ease-in-out infinite;

        &.steam-1 {
          left: 30rpx;
          top: -40rpx;
          animation-delay: 0s;
        }

        &.steam-2 {
          left: 50rpx;
          top: -35rpx;
          animation-delay: 0.5s;
        }

        &.steam-3 {
          left: 70rpx;
          top: -30rpx;
          animation-delay: 1s;
        }
      }
    }
  }

  @keyframes steam-rise {
    0% {
      opacity: 0.8;
      transform: translateY(0) scale(1);
    }
    50% {
      opacity: 0.4;
      transform: translateY(-20rpx) scale(1.2);
    }
    100% {
      opacity: 0;
      transform: translateY(-40rpx) scale(0.8);
    }
  }

  .header-actions {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    padding: 60rpx 40rpx 20rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .time-display {
      background-color: rgba(255, 255, 255, 0.8);
      width: 30rpx;
      height: 30rpx;
      padding: 20rpx;
      border-radius: 50%;
    }

    .action-buttons {
      display: flex;
      gap: 16rpx;

      .action-btn {
        width: 64rpx;
        height: 64rpx;
        background-color: rgba(255, 255, 255, 0.9);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        backdrop-filter: blur(10px);

        .action-icon {
          font-size: 32rpx;
          font-weight: bold;
          color: #333;

          &.back-icon {
            font-size: 48rpx;
            margin-left: -4rpx;
          }

          &.share-icon {
            font-size: 28rpx;
          }

          &.delete-icon {
            font-size: 24rpx;
          }
        }

        &:active {
          background-color: rgba(255, 255, 255, 0.7);
        }
      }
    }
  }
}

.content-section {
  background-color: white;
  border-radius: 32rpx 32rpx 0 0;
  margin-top: -32rpx;
  position: relative;
  z-index: 10;
  padding: 40rpx 48rpx 60rpx;
  min-height: calc(100vh - 368rpx);
}

.category-tag {
  margin-bottom: 32rpx;

  .category-text {
    background-color: #2c3e50;
    color: white;
    padding: 12rpx 24rpx;
    border-radius: 24rpx;
    font-size: 24rpx;
    font-weight: 600;
    letter-spacing: 1rpx;
  }
}

.title-section {
  margin-bottom: 48rpx;

  .main-title {
    font-size: 48rpx;
    font-weight: 300;
    color: #2c3e50;
    line-height: 1.3;
    display: block;
  }
}

.summary-section {
  margin-bottom: 48rpx;
  padding-bottom: 32rpx;
  border-bottom: 1px solid #f0f0f0;

  .summary-label {
    display: block;
    font-size: 28rpx;
    color: #999;
    margin-bottom: 16rpx;
    font-weight: 400;
  }

  .summary-content {
    display: block;
    font-size: 36rpx;
    font-weight: 600;
    color: #2c3e50;
    line-height: 1.4;
  }
}

.detail-content {
  .content-block {
    margin-bottom: 48rpx;

    .block-title {
      display: block;
      font-size: 28rpx;
      color: #999;
      margin-bottom: 20rpx;
      font-weight: 400;
    }

    .block-content {
      display: block;
      font-size: 32rpx;
      color: #2c3e50;
      line-height: 1.6;
      font-weight: 400;
    }
  }
}

.bottom-divider {
  width: 200rpx;
  height: 8rpx;
  background-color: #2c3e50;
  border-radius: 4rpx;
  margin: 40rpx auto 0;
}
</style>