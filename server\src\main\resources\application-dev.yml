server:
  port: 8080
  servlet:
    context-path: /api

spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **********************************************************************************************************************
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD:Lx1024lh...asd}

cors:
  origins: http://localhost:5173

# AI服务配置 - 开发环境
langchain4j:
  community:
    dashscope:
      chat-model:
        api-key: ${DASHSCOPE_API_KEY:sk-869934Ek1s}
        model-name: deepseek-r1-distill-qwen-1.5b
      streaming-chat-model:
        api-key: ${DASHSCOPE_API_KEY:sk-869934Ek1s}
        model-name: deepseek-r1-distill-qwen-1.5b
      embedding-model:
        api-key: ${DASHSCOPE_API_KEY:sk-869934Ek1s}
        model-name: text-embedding-v3
  open-ai:
    chat-model:
      base-url: https://api.hunyuan.cloud.tencent.com/v1
      api-key: ${HUNYUAN_API_KEY:sk-JOD8aG4N5sj0Qlsyw2SqJJJzx82YF7rneyavhrLsTz4v9Dpa}
      model-name: hunyuan-lite

# Milvus配置 - 开发环境
#milvus:
#  uri: ${MILVUS_URI:http://localhost:19530}
#  token: ${MILVUS_TOKEN:root:Milvus}
#  enable: true
#  open-log: true  # 开发环境开启日志
#  db-name: default
#  username: ${MILVUS_USERNAME:root}
#  password: ${MILVUS_PASSWORD:Milvus}
#  packages:
#    - com.kibi.entity

# 开发环境日志配置
logging:
  level:
    com.kibi: DEBUG
    org.springframework.web: DEBUG
    org.mybatis: DEBUG

# 应用开发环境特定配置
app:
  environment: dev
  debug-mode: true
  dev-apis-enabled: true  # 启用开发调试接口
  mock-data-enabled: true # 允许创建模拟数据 