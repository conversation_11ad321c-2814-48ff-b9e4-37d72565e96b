package com.kibi.entity;

import com.alibaba.fastjson2.JSONArray;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.kibi.config.JsonArrayTypeHandler;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

@Data
@TableName("tb_partner")
public class Partner {

    @TableId(value = "user_id", type = IdType.INPUT)
    private Integer userId;
    private String nickname;

    @TableField(typeHandler = JsonArrayTypeHandler.class)
    private JSONArray traits;

    private String style;

    @TableField(typeHandler = JsonArrayTypeHandler.class)
    private JSONArray description;
    private Integer spontaneous;
    private Integer collaborative;
    private Integer realist;
    private Integer logical;
    private Integer analytical;
    private Integer introvert;

    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
}
