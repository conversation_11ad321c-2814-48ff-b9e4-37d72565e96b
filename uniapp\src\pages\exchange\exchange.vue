<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useUserStore } from '@/stores'
import { http } from '@/utils/http'
import { mockExchangeData } from '@/mock/exchangeData'
import type { Post, Comment, Category, PublishForm } from '@/types/community'

const userStore = useUserStore()

// 分类数据
const categories = ref<Category[]>([
  { name: '全部', key: 'all' },
  { name: '日常分享', key: 'daily' },
  { name: '心情感悟', key: 'mood' },
  { name: '学习交流', key: 'study' },
  { name: '技能分享', key: 'skill' },
  { name: '问题求助', key: 'help' },
  { name: '其他', key: 'other' }
])

const currentCategory = ref<number>(0)
const postsList = ref<Post[]>([])
const loading = ref<boolean>(false)
const loadingMore = ref<boolean>(false)
const refreshing = ref<boolean>(false)
const noMoreData = ref<boolean>(false)
const currentPage = ref<number>(1)

// 发布相关
const showPublish = ref<boolean>(false)
const publishForm = ref<PublishForm>({
  categoryIndex: 0,
  title: '',
  content: '',
  images: []
})

// 详情相关
const showDetail = ref<boolean>(false)
const currentPost = ref<Post | null>(null)
const comments = ref<Comment[]>([])
const commentText = ref<string>('')

// 切换分类
const switchCategory = (index: number) => {
  currentCategory.value = index
  currentPage.value = 1
  postsList.value = []
  noMoreData.value = false
  loadPosts()
}

// 加载帖子列表
const loadPosts = async () => {
  if (loading.value || loadingMore.value) return
  
  if (currentPage.value === 1) {
    loading.value = true
  } else {
    loadingMore.value = true
  }

  try {
    // 使用模拟数据
    const category = categories.value[currentCategory.value]
    const categoryPosts = mockExchangeData.posts[category.key] || []
    
    // 模拟分页
    const startIndex = (currentPage.value - 1) * 10
    const endIndex = startIndex + 10
    const newPosts = categoryPosts.slice(startIndex, endIndex)
    
    if (currentPage.value === 1) {
      postsList.value = newPosts
    } else {
      postsList.value.push(...newPosts)
    }
    
    if (newPosts.length < 10) {
      noMoreData.value = true
    }
    
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 500))
    
  } catch (error) {
    console.error('加载帖子失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'none'
    })
  } finally {
    loading.value = false
    loadingMore.value = false
    refreshing.value = false
  }
}

// 加载更多
const loadMorePosts = () => {
  if (!noMoreData.value && !loadingMore.value) {
    currentPage.value++
    loadPosts()
  }
}

// 刷新
const refreshPosts = () => {
  refreshing.value = true
  currentPage.value = 1
  noMoreData.value = false
  loadPosts()
}

// 显示发布弹窗
const showPublishModal = () => {
  if (!userStore.profile) {
    uni.showToast({
      title: '请先登录',
      icon: 'none'
    })
    return
  }
  showPublish.value = true
}

// 隐藏发布弹窗
const hidePublishModal = () => {
  showPublish.value = false
  // 重置表单
  publishForm.value = {
    categoryIndex: 0,
    title: '',
    content: '',
    images: []
  }
}

// 分类选择
const onCategoryChange = (e: any) => {
  publishForm.value.categoryIndex = e.detail.value
}

// 选择图片
const chooseImage = () => {
  uni.chooseImage({
    count: 9 - publishForm.value.images.length,
    sizeType: ['compressed'],
    sourceType: ['album', 'camera'],
    success: (res) => {
      publishForm.value.images.push(...res.tempFilePaths)
    }
  })
}

// 删除图片
const removeImage = (index: number) => {
  publishForm.value.images.splice(index, 1)
}

// 发布帖子
const publishPost = async () => {
  if (!publishForm.value.title.trim()) {
    uni.showToast({
      title: '请输入标题',
      icon: 'none'
    })
    return
  }
  
  if (!publishForm.value.content.trim()) {
    uni.showToast({
      title: '请输入内容',
      icon: 'none'
    })
    return
  }

  try {
    uni.showLoading({
      title: '发布中...',
      mask: true
    })

    // 模拟发布成功
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 创建新帖子并添加到列表顶部
    const category = categories.value[publishForm.value.categoryIndex]
    const newPost = {
      id: Date.now(),
      title: publishForm.value.title,
      content: publishForm.value.content,
      authorName: userStore.profile?.nickname || '匿名用户',
      authorAvatar: userStore.profile?.avatar || 'https://picsum.photos/100/100?random=999',
      categoryName: category.name,
      createTime: new Date().toISOString(),
      likeCount: 0,
      commentCount: 0,
      isLiked: false,
      images: publishForm.value.images
    }
    
    postsList.value.unshift(newPost)

    uni.hideLoading()
    uni.showToast({
      title: '发布成功',
      icon: 'success'
    })
    hidePublishModal()
    
  } catch (error) {
    uni.hideLoading()
    uni.showToast({
      title: '发布失败',
      icon: 'none'
    })
  }
}

// 查看帖子详情
const viewPost = async (post: any) => {
  currentPost.value = post
  showDetail.value = true
  
  // 加载评论 - 使用模拟数据
  try {
    comments.value = mockExchangeData.comments[post.id] || []
  } catch (error) {
    console.error('加载评论失败:', error)
  }
}

// 隐藏详情弹窗
const hideDetailModal = () => {
  showDetail.value = false
  currentPost.value = null
  comments.value = []
  commentText.value = ''
}

// 提交评论
const submitComment = async () => {
  if (!commentText.value.trim()) {
    uni.showToast({
      title: '请输入评论内容',
      icon: 'none'
    })
    return
  }

  try {
    // 模拟提交评论
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 添加新评论到列表
    comments.value.unshift({
      id: Date.now(),
      content: commentText.value,
      authorName: userStore.profile?.nickname || '匿名用户',
      authorAvatar: userStore.profile?.avatar || 'https://picsum.photos/100/100?random=999',
      createTime: new Date().toISOString()
    })
    
    // 更新帖子评论数
    if (currentPost.value) {
      currentPost.value.commentCount++
    }
    
    commentText.value = ''
    uni.showToast({
      title: '评论成功',
      icon: 'success'
    })
    
  } catch (error) {
    uni.showToast({
      title: '评论失败',
      icon: 'none'
    })
  }
}

// 点赞/取消点赞
const toggleLike = async (post: any) => {
  if (!userStore.profile) {
    uni.showToast({
      title: '请先登录',
      icon: 'none'
    })
    return
  }

  try {
    uni.showToast({
      title: '点赞+1',
      icon: 'none'
    })
    // 模拟点赞操作
    // await new Promise(resolve => setTimeout(resolve, 200))
    
    // post.isLiked = !post.isLiked
    // post.likeCount += post.isLiked ? 1 : -1
    
  } catch (error) {
    uni.showToast({
      title: '操作失败',
      icon: 'none'
    })
  }
}

// 分享帖子
const sharePost = (post: any) => {
  uni.showActionSheet({
    itemList: ['分享到微信', '复制链接'],
    success: (res) => {
      if (res.tapIndex === 0) {
        // 分享到微信
        uni.showToast({
          title: '分享功能开发中',
          icon: 'none'
        })
      } else if (res.tapIndex === 1) {
        // 复制链接
        uni.setClipboardData({
          data: 'https://lintu.tech/pages/exchange/exchange',
          success: () => {
            uni.showToast({
              title: '链接已复制',
              icon: 'success'
            })
          }
        })
      }
    }
  })
}

// 预览图片
const previewImage = (images: string[], current: number) => {
  uni.previewImage({
    urls: images,
    current: current
  })
}

// 格式化时间
const formatTime = (timeStr: string) => {
  const time = new Date(timeStr)
  const now = new Date()
  const diff = now.getTime() - time.getTime()
  
  const minute = 60 * 1000
  const hour = 60 * minute
  const day = 24 * hour
  
  if (diff < minute) {
    return '刚刚'
  } else if (diff < hour) {
    return `${Math.floor(diff / minute)}分钟前`
  } else if (diff < day) {
    return `${Math.floor(diff / hour)}小时前`
  } else if (diff < 7 * day) {
    return `${Math.floor(diff / day)}天前`
  } else {
    return time.toLocaleDateString()
  }
}

// 处理图片加载错误
const handleImageError = (e: any) => {
  console.log('图片加载失败:', e)
  // 可以设置默认图片
  // e.target.src = '/static/default-image.png'
}

// 返回上一页
const goBack = () => {
  if (getCurrentPages().length == 1) {
    uni.reLaunch({ url: '/pages/ai/ai' })
  } else {
    uni.navigateBack({ delta: 1})
  }
}

onMounted(() => {
  loadPosts()
})
</script>

<template>
  <view class="exchange-container">
    <!-- Header -->
    <view class="header">
      <view class="header-left" @tap="goBack">
        <image src="/static/ai/back.png" class="back-icon" />
      </view>
      <view class="header-title">社区交流</view>
      <view class="header-right" @tap="showPublishModal">
        <image src="/static/ai/pulish.png" class="edit-icon" />
      </view>
    </view>

    <!-- Category Tabs -->
    <view class="category-tabs">
      <scroll-view scroll-x="true" class="tabs-scroll">
        <view class="tabs-container">
          <view 
            v-for="(category, index) in categories" 
            :key="index"
            class="tab-item"
            :class="{ active: currentCategory === index }"
            @tap="switchCategory(index)"
          >
            <text class="tab-text">{{ category.name }}</text>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- Posts List -->
    <scroll-view 
      scroll-y="true" 
      class="posts-container"
      @scrolltolower="loadMorePosts"
      refresher-enabled
      @refresherrefresh="refreshPosts"
      :refresher-triggered="refreshing"
    >
      <view class="posts-list">
        <view 
          v-for="post in postsList" 
          :key="post.id"
          class="post-item"
          @tap="viewPost(post)"
        >
          <!-- Post Header -->
          <view class="post-header">
            <view class="author-info">
              <image :src="post.authorAvatar" class="author-avatar" />
              <view class="author-details">
                <text class="author-name">{{ post.authorName }}</text>
                <text class="post-time">{{ formatTime(post.createTime) }}</text>
              </view>
            </view>
            <view class="post-category">
              <text class="category-tag">{{ post.categoryName }}</text>
            </view>
          </view>

          <!-- Post Content -->
          <view class="post-content">
            <view class="post-title">{{ post.title }}</view>
            <text class="post-excerpt">{{ post.content }}</text>
            
            <!-- Post Images -->
            <view v-if="post.images && post.images.length > 0" class="post-images">
              <image 
                v-for="(img, imgIndex) in post.images.slice(0, 3)" 
                :key="imgIndex"
                :src="img" 
                class="post-image"
                mode="aspectFill"
                @tap.stop="previewImage(post.images, imgIndex)"
                @error="handleImageError"
              />
              <view v-if="post.images.length > 3" class="more-images">
                <text>+{{ post.images.length - 3 }}</text>
              </view>
            </view>
          </view>

          <!-- Post Actions -->
          <view class="post-actions">
            <view class="action-item" @tap.stop="toggleLike(post)">
              <!-- <image 
                :src="post.isLiked ? '/static/ai/heart-filled.png' : '/static/ai/heart.png'" 
                class="action-icon" 
              /> -->
              <text class="action-text">♥ {{ post.likeCount || 0 }}</text>
            </view>
            <view class="action-item" @tap.stop="viewPost(post)">
              <!-- <image src="/static/ai/comment.png" class="action-icon" /> -->
              <text class="action-text">💬 {{ post.commentCount || 0 }}</text>
            </view>
            <view class="action-item" @tap.stop="sharePost(post)">
              <image src="/static/ai/share.png" class="action-icon" />
              <text class="action-text">分享</text>
            </view>
          </view>
        </view>
      </view>

      <!-- Loading More -->
      <view v-if="loadingMore" class="loading-more">
        <text>加载更多...</text>
      </view>

      <!-- No More Data -->
      <view v-if="noMoreData && postsList.length > 0" class="no-more">
        <text>没有更多内容了</text>
      </view>

      <!-- Empty State -->
      <view v-if="!loading && postsList.length === 0" class="empty-state">
        <text class="empty-text">还没有帖子，快来发布第一个吧！</text>
      </view>
    </scroll-view>

    <!-- Publish Modal -->
    <view v-if="showPublish" class="publish-modal-overlay" @tap="hidePublishModal">
      <view class="publish-modal" @tap.stop>
        <view class="publish-header">
          <text class="publish-title">发布帖子</text>
          <view class="publish-close" @tap="hidePublishModal">
            <image src="/static/ai/close.png" class="close-icon" />
          </view>
        </view>

        <!-- Category Selection -->
        <view class="form-group">
          <text class="form-label">选择分类</text>
          <picker 
            :range="categories" 
            range-key="name" 
            :value="publishForm.categoryIndex"
            @change="onCategoryChange"
          >
            <view class="picker-input">
              {{ categories[publishForm.categoryIndex]?.name || '请选择分类' }}
            </view>
          </picker>
        </view>

        <!-- Title Input -->
        <view class="form-group">
          <text class="form-label">标题</text>
          <input 
            v-model="publishForm.title" 
            placeholder="请输入标题..." 
            class="title-input"
            maxlength="50"
          />
        </view>

        <!-- Content Input -->
        <view class="form-group">
          <text class="form-label">内容</text>
          <textarea 
            v-model="publishForm.content" 
            placeholder="分享你的想法..." 
            class="content-input"
            maxlength="500"
            auto-height
          />
        </view>

        <!-- Image Upload -->
        <view class="form-group">
          <text class="form-label">图片 (最多9张)</text>
          <view class="image-upload">
            <view 
              v-for="(img, index) in publishForm.images" 
              :key="index"
              class="upload-item"
            >
              <image :src="img" class="upload-image" mode="aspectFill" />
              <view class="delete-btn" @tap="removeImage(index)">
                <text>×</text>
              </view>
            </view>
            <view 
              v-if="publishForm.images.length < 9" 
              class="upload-btn" 
              @tap="chooseImage"
            >
              <text class="upload-text">+</text>
            </view>
          </view>
        </view>

        <!-- Publish Button -->
        <view class="publish-actions">
          <view class="publish-btn" @tap="publishPost">
            <text class="publish-btn-text">发布</text>
          </view>
        </view>
      </view>
    </view>

    <!-- Post Detail Modal -->
    <view v-if="showDetail" class="detail-modal-overlay" @tap="hideDetailModal">
      <view class="detail-modal" @tap.stop>
        <view class="detail-header">
          <text class="detail-title">帖子详情</text>
          <view class="detail-close" @tap="hideDetailModal">
            <image src="/static/ai/close.png" class="close-icon" />
          </view>
        </view>

        <scroll-view scroll-y="true" class="detail-content">
          <!-- Post Detail -->
          <view v-if="currentPost" class="post-detail">
            <view class="detail-post-header">
              <image :src="currentPost.authorAvatar" class="detail-author-avatar" />
              <view class="detail-author-info">
                <text class="detail-author-name">{{ currentPost.authorName }}</text>
                <text class="detail-post-time">{{ formatTime(currentPost.createTime) }}</text>
              </view>
            </view>
            
            <view class="detail-post-title">{{ currentPost.title }}</view>
            <view class="detail-post-content">
              <text>{{ currentPost.content }}</text>
            </view>
            
            <!-- Post Images -->
            <view v-if="currentPost.images && currentPost.images.length > 0" class="detail-images">
              <image 
                v-for="(img, imgIndex) in currentPost.images" 
                :key="imgIndex"
                :src="img" 
                class="detail-image"
                mode="aspectFill"
                @tap="previewImage(currentPost.images, imgIndex)"
              />
            </view>
          </view>

          <!-- Comments -->
          <view class="comments-section">
            <text class="comments-title">评论 ({{ comments.length }})</text>
            <view 
              v-for="comment in comments" 
              :key="comment.id"
              class="comment-item"
            >
              <image :src="comment.authorAvatar" class="comment-avatar" />
              <view class="comment-content">
                <view class="comment-author">{{ comment.authorName }}<text class="comment-time">{{ formatTime(comment.createTime) }}</text></view>
                <text class="comment-text">{{ comment.content }}</text>
              </view>
            </view>
          </view>
        </scroll-view>

        <!-- Comment Input -->
        <view class="comment-input-bar">
          <input 
            v-model="commentText" 
            placeholder="写下你的评论..." 
            class="comment-input"
            @confirm="submitComment"
          />
          <view class="comment-send-btn" @tap="submitComment">
            <text>发送</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.exchange-container {
  min-height: 100vh;
  background: rgba(26, 26, 46, 0.9);
  color: white;
  position: relative;
  overflow-x: hidden;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40rpx 30rpx 20rpx;
  position: sticky;
  top: 0;
  z-index: 100;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(15rpx);
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.1);
}

.header-left, .header-right {
  width: 50rpx;
}

.back-icon, .edit-icon {
  width: 40rpx;
  height: 40rpx;
}

.header-title {
  font-size: 36rpx;
  font-weight: 600;
  text-align: center;
}

.category-tabs {
  padding: 20rpx 0;
  background: rgba(255, 255, 255, 0.05);
}

.tabs-scroll {
  white-space: nowrap;
}

.tabs-container {
  display: flex;
  flex-wrap: wrap;
  padding: 0 30rpx;
  gap: 20rpx;
}

.tab-item {
  padding: 10rpx 20rpx;
  border-radius: 10rpx;
  background: rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  white-space: nowrap;
  
  &.active {
    background: rgba(255, 255, 255, 0.5);
    transform: scale(1.05);
  }
}

.tab-text {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
}

.posts-container {
  flex: 1;
  height: calc(100vh - 200rpx);
}

.posts-list {
  padding: 20rpx 30rpx;
}

.post-item {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  color: #333;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.post-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.author-info {
  display: flex;
  align-items: center;
}

.author-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.author-name {
  font-size: 28rpx;
  font-weight: 600;
  margin: 0 12rpx 6rpx 0;
}

.post-time {
  font-size: 24rpx;
  color: #666;
}

.category-tag {
  background: #e3f2fd;
  color: #1976d2;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
}

.post-content {
  margin-bottom: 20rpx;
}

.post-title {
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 10rpx;
  line-height: 1.4;
}

.post-excerpt {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 20rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.post-images {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
  position: relative;
}

.post-image {
  width: 200rpx;
  height: 200rpx;
  border-radius: 12rpx;
}

.more-images {
  width: 200rpx;
  height: 200rpx;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 32rpx;
  position: absolute;
  top: 0;
  right: 0;
}

.post-actions {
  display: flex;
  justify-content: space-around;
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.action-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 10rpx;
  border-radius: 8rpx;
  transition: background-color 0.2s;
  
  &:active {
    background-color: #f5f5f5;
  }
}

.action-icon {
  width: 32rpx;
  height: 32rpx;
}

.action-text {
  font-size: 24rpx;
  color: #666;
}

.loading-more, .no-more, .empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx;
  color: rgba(255, 255, 255, 0.7);
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
}

/* 发布弹窗样式 */
.publish-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
}

.publish-modal {
  width: 90%;
  max-width: 600rpx;
  max-height: 80vh;
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
}

.publish-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.publish-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.close-icon {
  width: 32rpx;
  height: 32rpx;
}

.form-group {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
  display: block;
}

.picker-input {
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  color: #333;
}

.title-input, .content-input {
  width: 100%;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: none;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
}

.content-input {
  min-height: 200rpx;
}

.image-upload {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.upload-item {
  position: relative;
  width: 150rpx;
  height: 150rpx;
}

.upload-image {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
}

.delete-btn {
  position: absolute;
  top: -10rpx;
  right: -10rpx;
  width: 40rpx;
  height: 40rpx;
  background: #ff4444;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24rpx;
}

.upload-btn {
  width: 150rpx;
  height: 150rpx;
  border: 2rpx dashed #ddd;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
}

.upload-text {
  font-size: 48rpx;
  color: #999;
}

.publish-actions {
  padding: 30rpx;
}

.publish-btn {
  width: 100%;
  height: 80rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.publish-btn-text {
  color: white;
  font-size: 32rpx;
  font-weight: 600;
}

/* 详情弹窗样式 */
.detail-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1001;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
}

.detail-modal {
  width: 95%;
  height: 85vh;
  background: white;
  border-radius: 20rpx;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.detail-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.detail-content {
  flex: 1;
  padding: 30rpx;
}

.post-detail {
  margin-bottom: 40rpx;
  padding-bottom: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.detail-post-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.detail-author-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.detail-author-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin: 0 12rpx 6rpx 0;
}

.detail-post-time {
  font-size: 24rpx;
  color: #666;
}

.detail-post-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
  line-height: 1.4;
}

.detail-post-content {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 20rpx;
  width: 90%;
}

.detail-images {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
}

.detail-image {
  width: 200rpx;
  height: 200rpx;
  border-radius: 12rpx;
}

.comments-section {
  margin-top: 20rpx;
}

.comments-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.comment-item {
  display: flex;
  margin: 30rpx 0;
  width: 90%;
}

.comment-avatar {
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.comment-content {
  flex: 1;
}

.comment-author {
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.comment-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 8rpx;
}

.comment-time {
  margin-left: 20rpx;
  font-size: 22rpx;
  color: #999;
}

.comment-input-bar {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #f0f0f0;
  background: white;
}

.comment-input {
  flex: 1;
  height: 60rpx;
  padding: 0 20rpx;
  background: #f8f9fa;
  border-radius: 30rpx;
  border: none;
  font-size: 28rpx;
  margin-right: 20rpx;
}

.comment-send-btn {
  padding: 16rpx 32rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 10rpx;
  color: white;
  font-size: 26rpx;
}
</style>