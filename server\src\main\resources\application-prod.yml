server:
  port: ${SERVER_PORT}
  servlet:
    context-path: /api

spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    # 方案1：禁用SSL（当前激活）
    url: ${DB_URL:**********************************************************************************************************************}
    # 方案2：启用SSL但允许自签名证书（如需要SSL请使用此配置）
    # url: ${DB_URL:*******************************************************************************************************************************************************************************************}
    username: ${DB_USERNAME}
    password: ${DB_PASSWORD}
    # 生产环境数据库连接池配置
    druid:
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1 FROM DUAL
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false

cors:
  origins: https://lintu.tech

# AI服务配置 - 生产环境
langchain4j:
#  community:
#    dashscope:
#      chat-model:
#        api-key: ${DASHSCOPE_API_KEY}
#        model-name: ${DASHSCOPE_MODEL:deepseek-r1-distill-qwen-1.5b}
#      streaming-chat-model:
#        api-key: ${DASHSCOPE_API_KEY}
#        model-name: ${DASHSCOPE_MODEL:deepseek-r1-distill-qwen-1.5b}
#      embedding-model:
#        api-key: ${DASHSCOPE_API_KEY}
#        model-name: ${DASHSCOPE_EMBEDDING_MODEL:text-embedding-v3}
  open-ai:
    chat-model:
      base-url: ${HUNYUAN_BASE_URL:https://api.hunyuan.cloud.tencent.com/v1}
      api-key: ${HUNYUAN_API_KEY}
      model-name: ${HUNYUAN_MODEL:hunyuan-lite}

# Milvus配置 - 生产环境
#milvus:
#  uri: ${MILVUS_URI}
#  token: ${MILVUS_TOKEN}
#  enable: ${MILVUS_ENABLE:true}
#  open-log: false  # 生产环境关闭详细日志
#  db-name: ${MILVUS_DB_NAME}
#  username: ${MILVUS_USERNAME}
#  password: ${MILVUS_PASSWORD}
#  packages:
#    - com.kibi.entity

# 生产环境日志配置
logging:
  level:
    org.springframework: WARN
    com.kibi: INFO
    org.mybatis: WARN
  file:
    name: ${LOG_FILE_PATH:./skychart-prod.log}
  logback:
    rollingpolicy:
      max-file-size: 10MB
      max-history: 30

# 生产环境管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: never

# 应用生产环境特定配置
app:
  environment: prod
  debug-mode: false
  dev-apis-enabled: false  # 禁用开发调试接口
  mock-data-enabled: false # 禁止创建模拟数据 