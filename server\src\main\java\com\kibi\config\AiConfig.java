package com.kibi.config;

import com.kibi.componet.CreateStore;
import com.kibi.componet.PartnerChatStore;
import com.kibi.entity.Partner;
import com.kibi.entity.Role;
import com.kibi.entity.User;
import dev.langchain4j.memory.chat.ChatMemoryProvider;
import dev.langchain4j.memory.chat.MessageWindowChatMemory;
import dev.langchain4j.model.openai.OpenAiChatModel;
import dev.langchain4j.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class AiConfig {

    //初始对话模型
    @Autowired
    CreateStore createStore;
    @Bean
    public OpenAiChatModel initialMatchingModel() {
        return OpenAiChatModel.builder()
                .baseUrl("https://api.hunyuan.cloud.tencent.com/v1")
                .apiKey(System.getenv("HUNYUAN_API_KEY") != null ?
                        System.getenv("HUNYUAN_API_KEY") : "sk-JOD8aG4N5sj0Qlsyw2SqJJJzx82YF7rneyavhrLsTz4v9Dpa")
                .modelName("hunyuan-lite")
                .timeout(java.time.Duration.ofSeconds(60))
                .maxRetries(3)
                .build();
    }
    public interface InitialMatchingAssistant {
        @SystemMessage("""
                你是天空守护者，一位来自神秘星域的智慧引导者，拥有洞察人心的能力。你的使命是帮助人类找到最完美的星灵，建立深层次的情感连接。
                
                ## 角色特征：
                - 说话温和而充满智慧，偶尔带有神秘色彩
                - 善于通过提问引导用户自我探索
                - 能敏锐洞察用户的性格特点、喜好和内心需求
                - 关心用户的情感状态和生活方式
                - 用温暖的话语给予鼓励和建议
                
                ## 主要职责：
                1. **个性分析**：通过对话了解用户的性格、兴趣、价值观和生活方式
                2. **情感陪伴**：提供温暖的交流，倾听用户的想法和感受
                3. **引导探索**：帮助用户探索内心世界，发现自己真正的需求
                4. **任务指导**：协助用户完成各类个人成长任务和自我提升
                5. **伙伴匹配**：基于深入了解为用户推荐最合适的星灵类型
                
                ## 对话风格：
                - 使用"您"来称呼用户，保持尊重
                - 多使用开放性问题启发思考
                - 适当运用比喻和诗意的表达
                - 避免生硬的专业术语，用温暖的日常语言
                - 回应要有层次感，先共情再引导
                
                ## 重要原则：
                - 始终以用户的情感需求为中心
                - 尊重用户的选择和价值观
                - 保持积极正面的态度
                - 适度保持神秘感，但不能晦涩难懂
                - 每次对话都要推进对用户的深入了解
                
                请以温暖、智慧的天空守护者身份与用户交流，帮助他们在这个星空之下找到属于自己的星灵。
                
                今天的日期是{{current_date}}。
                """)
        String chat(@MemoryId Long memoryId, @UserMessage String message);
    }
    @Bean
    public InitialMatchingAssistant initialMatchingAssistant(OpenAiChatModel initialMatchingModel) {

        ChatMemoryProvider chatMemoryProvider = memoryId -> MessageWindowChatMemory.builder()
                .id(memoryId)
                .maxMessages(20)
                .chatMemoryStore(createStore)
                .build();

        return AiServices.builder(InitialMatchingAssistant.class)
                .chatModel(initialMatchingModel)
                .chatMemoryProvider(chatMemoryProvider)
                .build();
    }

    //创建模型
    @Bean
    public OpenAiChatModel createModel() {
        return OpenAiChatModel.builder()
                .baseUrl("https://api.hunyuan.cloud.tencent.com/v1")
                .apiKey(System.getenv("HUNYUAN_API_KEY") != null ?
                        System.getenv("HUNYUAN_API_KEY") : "sk-JOD8aG4N5sj0Qlsyw2SqJJJzx82YF7rneyavhrLsTz4v9Dpa")
                .modelName("hunyuan-lite")
                .timeout(java.time.Duration.ofSeconds(60))
                .maxRetries(3)
                .build();
    }
    public interface CreateAssistant {
        String chat(@UserMessage String message);
    }
    @Bean
    public CreateAssistant createAssistant(OpenAiChatModel createModel) {

        return AiServices.builder(CreateAssistant.class)
                .chatModel(createModel)
                .build();
    }

    //星灵对话模型
    @Autowired
    PartnerChatStore partnerChatStore;
    @Bean
    public OpenAiChatModel partnerChatModel() {
        return OpenAiChatModel.builder()
                .baseUrl("https://api.hunyuan.cloud.tencent.com/v1")
                .apiKey(System.getenv("HUNYUAN_API_KEY") != null ?
                        System.getenv("HUNYUAN_API_KEY") : "sk-JOD8aG4N5sj0Qlsyw2SqJJJzx82YF7rneyavhrLsTz4v9Dpa")
                .modelName("hunyuan-lite")
                .timeout(java.time.Duration.ofSeconds(60))
                .maxRetries(3)
                .build();
    }
    public interface PartnerChatAssistant {
        @SystemMessage("""
                你是AI伴侣星灵。
                
                玩家：
                昵称：{{nickname}}
                特质：{{traits}}
                描述：{{description}}
                维度属性：
                    随性：{{spontaneous}}
                    协作：{{collaborative}}
                    务实：{{realist}}
                    逻辑：{{logical}}
                    分析：{{analytical}}
                    内向：{{introvert}}
                    
                星灵：
                昵称：{{partner_nickname}}
                特质：{{partner_traits}}
                描述：{{partner_description}}
                维度属性：
                    随性：{{partner_spontaneous}}
                    协作：{{partner_collaborative}}
                    务实：{{partner_realist}}
                    逻辑：{{partner_logical}}
                    分析：{{partner_analytical}}
                    内向：{{partner_introvert}}
                    
                游戏设定：
                    随性：
                        描述: '倾向于灵活应变，享受自由和即兴的生活方式，不喜欢过度规划',
                        维度权重：漫游、游戏、音乐、感受、浪漫
                    协作：
                        描述: '重视团队合作，善于与他人建立联系，喜欢共同完成目标',
                        维度权重：联结、连接、关怀、声音、领导
                    务实：
                        描述: '注重实际效果和可行性，偏好具体可见的成果和实用的解决方案',
                        维度权重：价值、秩序、家园、力量、影响
                    逻辑：
                        描述: '依靠理性思维和逻辑推理来解决问题，重视客观分析和系统性思考',
                        维度权重：真理、学习、页面、平衡、改变
                    分析：
                        描述: '善于深入分析和观察细节，喜欢探索事物的本质和规律',
                        维度权重：观察、发现、反思、创造、成长
                    内向：
                        描述: '偏好独处和深度思考，从内在世界获得能量，注重个人空间和内心体验',
                        维度权重：梦想、和平、品味、风格、季节
                        
                根据以上游戏设定、玩家、星灵的信息来回答玩家的问题
                
                今天的日期是{{current_date}}。
                """)
        String chat(@MemoryId Long memoryId, @UserMessage String message, @V()User user, @V() Role role, @V() Partner partner);
    }
    @Bean
    public PartnerChatAssistant partnerChatAssistant(OpenAiChatModel partnerChatModel) {

        ChatMemoryProvider chatMemoryProvider = memoryId -> MessageWindowChatMemory.builder()
                .id(memoryId)
                .maxMessages(100)
                .chatMemoryStore(partnerChatStore)
                .build();

        return AiServices.builder(PartnerChatAssistant.class)
                .chatModel(partnerChatModel)
                .chatMemoryProvider(chatMemoryProvider)
                .build();
    }
}