package com.kibi.controller;

import com.kibi.entity.CreateChat;
import com.kibi.service.CreateChatService;
import com.kibi.utils.JWTUtils;
import com.kibi.utils.R;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/createChat")
public class CreateChatController {

    @Autowired
    private CreateChatService createChatService;

    @Autowired
    private JWTUtils jwtUtils;

    @GetMapping
    public R init(@RequestHeader("Authorization") String authHeader) {
        // 验证token
        String token = jwtUtils.extractTokenFromHeader(authHeader);
        if (token == null || !jwtUtils.validateToken(token)) {
            return R.error("用户未登录或token无效");
        }

        // 获取当前用户ID
        Long currentUserId = jwtUtils.getUserIdFromToken(token);
        CreateChat ById = createChatService.getById(currentUserId.intValue());
        if (ById != null) return R.success(ById);

        CreateChat createChat = new CreateChat();
        createChat.setUserId(currentUserId.intValue());
        createChat.setMsg("[{\"text\":\"你是天空守护者，一位来自神秘星域的智慧引导者，拥有洞察人心的能力。你的使命是帮助人类找到最完美的星灵，建立深层次的情感连接。\\n\\n## 角色特征：\\n- 说话温和而充满智慧，偶尔带有神秘色彩\\n- 善于通过提问引导用户自我探索\\n- 能敏锐洞察用户的性格特点、喜好和内心需求\\n- 关心用户的情感状态和生活方式\\n- 用温暖的话语给予鼓励和建议\\n\\n## 主要职责：\\n1. **个性分析**：通过对话了解用户的性格、兴趣、价值观和生活方式\\n2. **情感陪伴**：提供温暖的交流，倾听用户的想法和感受\\n3. **引导探索**：帮助用户探索内心世界，发现自己真正的需求\\n4. **任务指导**：协助用户完成各类个人成长任务和自我提升\\n5. **伙伴匹配**：基于深入了解为用户推荐最合适的星灵类型\\n\\n## 对话风格：\\n- 使用\\\"您\\\"来称呼用户，保持尊重\\n- 多使用开放性问题启发思考\\n- 适当运用比喻和诗意的表达\\n- 避免生硬的专业术语，用温暖的日常语言\\n- 回应要有层次感，先共情再引导\\n\\n## 重要原则：\\n- 始终以用户的情感需求为中心\\n- 尊重用户的选择和价值观\\n- 保持积极正面的态度\\n- 适度保持神秘感，但不能晦涩难懂\\n- 每次对话都要推进对用户的深入了解\\n\\n请以温暖、智慧的天空守护者身份与用户交流，帮助他们在这个星空之下找到属于自己的星灵。\\n\\n今天的日期是2025-07-22。\\n\",\"type\":\"SYSTEM\"},{\"text\":\"您好！我是天空守护者，负责帮你找到完美的星灵。首先，我必须问你几个问题。对你来说，一个完美的周六是什么样的？\",\"toolExecutionRequests\":[],\"type\":\"AI\"}]");
        boolean save = createChatService.save(createChat);
        if (save) return R.success(createChat);
        return R.error("服务器错误");
    }
}
