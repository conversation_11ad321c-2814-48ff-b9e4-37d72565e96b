<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="6aa84011-1a50-40b8-a9ec-3d2c2a460c85" name="更改" comment="">
      <change beforePath="$PROJECT_DIR$/src/main/java/com/kibi/config/AiConfig.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/kibi/config/AiConfig.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/kibi/controller/AiController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/kibi/controller/AiController.java" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/.." />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="D:\apache-maven-3.9.9" />
        <option name="localRepository" value="D:\apache-maven-3.9.9\repository" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="D:\apache-maven-3.9.9\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="MavenRunner">
    <option name="skipTests" value="true" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 5
}</component>
  <component name="ProjectId" id="303lt4su9PXiQiTYPJ9Ar7gfxw5" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="1" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Maven.server [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.server [package].executor&quot;: &quot;Run&quot;,
    &quot;Maven.server [validate].executor&quot;: &quot;Run&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;Spring Boot.ServerApplication.executor&quot;: &quot;Run&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/code/skychart/server/src/main/java/com/kibi/controller&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;MavenSettings&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\code\skychart\server\src\main\java\com\kibi\controller" />
      <recent name="D:\code\skychart\server\src\main\java\com\kibi\entity" />
      <recent name="D:\code\skychart\server\src\main\java\com\kibi\componet" />
      <recent name="D:\code\skychart\server\src\main\java\com\kibi\config" />
      <recent name="D:\code\skychart\server\src\main\java\com\kibi\utils" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="com.kibi.controller" />
      <recent name="com.kibi.service.impl" />
      <recent name="com.kibi.service" />
      <recent name="com.kibi.mapper" />
      <recent name="com.kibi.entity" />
    </key>
  </component>
  <component name="RunManager">
    <configuration default="true" type="JetRunConfigurationType">
      <module name="ruoyi-generator" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ServerApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ruoyi" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.kibi.ServerApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-a94e463ab2e7-intellij.indexing.shared.core-IU-243.25659.59" />
        <option value="bundled-js-predefined-d6986cc7102b-1632447f56bf-JavaScript-IU-243.25659.59" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="6aa84011-1a50-40b8-a9ec-3d2c2a460c85" name="更改" comment="" />
      <created>1752864349680</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1752864349680</updated>
      <workItem from="1752864351500" duration="35546000" />
      <workItem from="1753107463435" duration="7166000" />
      <workItem from="1753186694110" duration="1071000" />
      <workItem from="1753192560269" duration="14262000" />
      <workItem from="1753222123956" duration="671000" />
      <workItem from="1753226053511" duration="7670000" />
      <workItem from="1753257764895" duration="9007000" />
      <workItem from="1753331181934" duration="4371000" />
      <workItem from="1753346021842" duration="11007000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/kibi/config/AiConfig.java</url>
          <line>20</line>
          <option name="timeStamp" value="1" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/kibi/config/AiConfig.java</url>
          <line>29</line>
          <option name="timeStamp" value="2" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>