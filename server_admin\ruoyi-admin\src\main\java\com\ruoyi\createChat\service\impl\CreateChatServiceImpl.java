package com.ruoyi.createChat.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.createChat.mapper.CreateChatMapper;
import com.ruoyi.createChat.domain.CreateChat;
import com.ruoyi.createChat.service.ICreateChatService;

/**
 * 初始对话Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
@Service
public class CreateChatServiceImpl implements ICreateChatService 
{
    @Autowired
    private CreateChatMapper createChatMapper;

    /**
     * 查询初始对话
     * 
     * @param userId 初始对话主键
     * @return 初始对话
     */
    @Override
    public CreateChat selectCreateChatByUserId(Long userId)
    {
        return createChatMapper.selectCreateChatByUserId(userId);
    }

    /**
     * 查询初始对话列表
     * 
     * @param createChat 初始对话
     * @return 初始对话
     */
    @Override
    public List<CreateChat> selectCreateChatList(CreateChat createChat)
    {
        return createChatMapper.selectCreateChatList(createChat);
    }

    /**
     * 新增初始对话
     * 
     * @param createChat 初始对话
     * @return 结果
     */
    @Override
    public int insertCreateChat(CreateChat createChat)
    {
        createChat.setCreateTime(DateUtils.getNowDate());
        return createChatMapper.insertCreateChat(createChat);
    }

    /**
     * 修改初始对话
     * 
     * @param createChat 初始对话
     * @return 结果
     */
    @Override
    public int updateCreateChat(CreateChat createChat)
    {
        return createChatMapper.updateCreateChat(createChat);
    }

    /**
     * 批量删除初始对话
     * 
     * @param userIds 需要删除的初始对话主键
     * @return 结果
     */
    @Override
    public int deleteCreateChatByUserIds(Long[] userIds)
    {
        return createChatMapper.deleteCreateChatByUserIds(userIds);
    }

    /**
     * 删除初始对话信息
     * 
     * @param userId 初始对话主键
     * @return 结果
     */
    @Override
    public int deleteCreateChatByUserId(Long userId)
    {
        return createChatMapper.deleteCreateChatByUserId(userId);
    }
}
